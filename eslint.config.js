import jsPlugin from '@eslint/js';
import reactPlugin from '@eslint-react/eslint-plugin';
import stylistic from '@stylistic/eslint-plugin';
import tanstackQueryPlugin from '@tanstack/eslint-plugin-query';
import tanstackRouterPlugin from '@tanstack/eslint-plugin-router';
import eslintConfigPrettier from 'eslint-config-prettier/flat';
import depend from 'eslint-plugin-depend';
import esXPlugin from 'eslint-plugin-es-x';
import eslintPluginImportX from 'eslint-plugin-import-x';
import jsxA11y from 'eslint-plugin-jsx-a11y';
import nodePlugin from 'eslint-plugin-n';
import perfectionist from 'eslint-plugin-perfectionist';
import promisePlugin from 'eslint-plugin-promise';
import reactCompiler from 'eslint-plugin-react-compiler';
import reactHooksPlugin from 'eslint-plugin-react-hooks';
import reactRefreshPlugin from 'eslint-plugin-react-refresh';
import simpleImportSortPlugin from 'eslint-plugin-simple-import-sort';
import unicornPlugin from 'eslint-plugin-unicorn';
import globals from 'globals';
import tsPlugin from 'typescript-eslint';

const RESTRICTED_SYNTAX = [
  {
    // ❌ Boolean(…)
    selector:
      'CallExpression[callee.name=Boolean][arguments.1.elements.length!=0]',
    message:
      'Prefer `!!…` over `Boolean(…)` because TypeScript infers a narrow literal boolean `type: true` instead of `type: boolean`.',
  },
  {
    // ❌ process.browser
    selector:
      'ExpressionStatement[expression.object.name=process][expression.property.name=browser]',
    message: '`process.browser` is deprecated, use `!!globalThis.window`',
  },
  {
    // ❌ useMemo(…, [])
    selector:
      'CallExpression[callee.name=useMemo][arguments.1.type=ArrayExpression][arguments.1.elements.length=0]',
    message:
      "`useMemo` with an empty dependency array can't provide a stable reference, use `useRef` instead.",
  },
];

const RESTRICTED_MODULES = [
  { name: 'moment', message: 'Use `dayjs/date-fns` instead.' },
  { name: 'classnames', message: 'Use `clsx` instead because he is faster.' },
  {
    name: 'lodash/isString.js',
    message: "Use `typeof yourVar === 'string'` instead.",
  },
  { name: 'lodash/isArray.js', message: 'Use `Array.isArray` instead.' },
  { name: 'lodash/flatten.js', message: 'Use `Array#flat()` instead.' },
  {
    name: 'lodash/compact.js',
    message: 'Use `Array#filter(Boolean)` instead.',
  },
  { name: 'lodash/identity.js', message: 'Use `(value) => value` instead.' },
];

const RESTRICTED_IMPORTS = [
  {
    name: 'react',
    importNames: ['FC', 'FunctionComponent'],
    message: 'Just type props and `ReactElement` as return type',
  },
  {
    name: 'react',
    importNames: ['PropsWithChildren'],
    message:
      '`PropsWithChildren` set `children` as optional, explicitly define `children` field in your type',
  },
];

export default [
  { files: ['**/*.{js,mjs,cjs,ts,jsx,tsx}'] },
  { ignores: ['**/generated/**', 'src/routeTree.gen.ts'] },
  { languageOptions: { globals: globals.browser } },
  depend.configs['flat/recommended'],
  jsPlugin.configs.recommended,
  ...tsPlugin.configs.strictTypeChecked,
  ...tsPlugin.configs.stylisticTypeChecked,
  nodePlugin.configs['flat/recommended-script'],
  reactPlugin.configs['recommended-typescript'],
  promisePlugin.configs['flat/recommended'],
  eslintPluginImportX.flatConfigs.recommended,
  eslintPluginImportX.flatConfigs.typescript,
  unicornPlugin.configs['recommended'],
  eslintConfigPrettier,
  stylistic.configs['disable-legacy'],
  ...tanstackQueryPlugin.configs['flat/recommended'],
  ...tanstackRouterPlugin.configs['flat/recommended'],
  {
    files: ['**/*.{js,mjs,cjs,jsx,mjsx,ts,tsx,mtsx}'],
    ...jsxA11y.flatConfigs.recommended,
    ...reactHooksPlugin.configs.recommended,
    languageOptions: {
      ...jsxA11y.flatConfigs.recommended.languageOptions,
      parser: tsPlugin.parser,
      parserOptions: {
        tsconfigRootDir: import.meta.dirname,
        projectService: true,
      },
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        ...globals.serviceworker,
        ...globals.browser,
      },
    },
    plugins: {
      perfectionist,
      'es-x': esXPlugin,
      'react-hooks': reactHooksPlugin,
      'simple-import-sort': simpleImportSortPlugin,
      'react-refresh': reactRefreshPlugin,
      'react-compiler': reactCompiler,
      '@stylistic': stylistic,
    },
    rules: {
      ...reactHooksPlugin.configs.recommended.rules,
      'react/react-in-jsx-scope': 'off',
      'promise/always-return': ['error', { ignoreLastCallback: true }],
      'simple-import-sort/imports': 'error',
      'simple-import-sort/exports': 'error',
      'import-x/first': 'error',
      'import-x/newline-after-import': 'error',
      'import-x/no-duplicates': 'error',
      'jsx-a11y/no-autofocus': 'off',
      'react-refresh/only-export-components': 'warn',
      'no-restricted-syntax': ['error', ...RESTRICTED_SYNTAX],
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-non-null-assertion': 'warn',
      '@typescript-eslint/no-restricted-imports': [
        'error',
        ...RESTRICTED_IMPORTS,
      ],
      '@typescript-eslint/no-misused-promises': [
        'error',
        { checksVoidReturn: { attributes: false, properties: false } },
      ],
      '@typescript-eslint/only-throw-error': 'warn',
      'promise/catch-or-return': ['error', { allowFinally: true }],
      'unicorn/no-null': 'off',
      'unicorn/prevent-abbreviations': [
        'error',
        {
          allowList: {
            Args: true,
            args: true,
            Props: true,
            props: true,
            Params: true,
            params: true,
            e2e: true,
            env: true,
            src: true,
            utils: true,
            util: true,
            Utils: true,
          },
        },
      ],
      'unicorn/filename-case': 'off',
      'unicorn/empty-brace-spaces': 'off',
      'unicorn/no-nested-ternary': 'off',
      'unicorn/number-literal-case': 'off',
      'perfectionist/sort-array-includes': ['error', { type: 'natural' }],
      'perfectionist/sort-enums': [
        'error',
        {
          type: 'natural',
          partitionByComment: true,
        },
      ],
      'perfectionist/sort-interfaces': [
        'error',
        {
          type: 'natural',
          partitionByNewLine: true,
        },
      ],
      'perfectionist/sort-intersection-types': [
        'error',
        {
          type: 'natural',
        },
      ],
      'perfectionist/sort-jsx-props': [
        'error',
        {
          type: 'natural',
        },
      ],
      'perfectionist/sort-maps': [
        'error',
        {
          type: 'natural',
        },
      ],
      'perfectionist/sort-object-types': [
        'error',
        {
          type: 'natural',
          partitionByNewLine: true,
        },
      ],
      'perfectionist/sort-sets': [
        'error',
        {
          type: 'natural',
        },
      ],
      'perfectionist/sort-union-types': [
        'error',
        {
          type: 'natural',
        },
      ],
      // Disallow specified modules when loaded by `import` declarations
      // https://github.com/eslint-community/eslint-plugin-n/blob/master/docs/rules/no-restricted-import.md
      'n/no-restricted-import': ['error', RESTRICTED_MODULES],
      // Disallow specified modules when loaded by require
      // https://github.com/eslint-community/eslint-plugin-n/blob/master/docs/rules/no-restricted-require.md
      'n/no-restricted-require': ['error', RESTRICTED_MODULES],
      'n/no-missing-import': 'off',
      'n/no-unsupported-features/node-builtins': 'off',
      'react-compiler/react-compiler': 'error',
    },
  },
  {
    files: ['**/*.{js,cjs,mjs,jsx}'],
    ...tsPlugin.configs.disableTypeChecked,
  },
];

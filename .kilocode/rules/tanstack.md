### **Tanstack Start and Tanstack Router**

- **Tanstack Router:** A fully type-safe, client-side router for React. Its core strengths are powerful data loading capabilities and first-class URL search parameter management.
- **Tanstack Start:** A full-stack framework built upon Tanstack Router. It adds Server-Side Rendering (SSR), streaming, Server Functions (RPCs), API routes, and deployment bundling, extending the router's type-safety to the full stack.
- **Key Principle:** The entire ecosystem is designed for maximum TypeScript inference. This requires specific patterns, primarily file-based routing and a one-time module declaration, to achieve end-to-end type safety.

---

## **1. Tanstack Router: Core Concepts**

### **1.1. File-Based Routing**

Routes are defined by files in `src/routes`. This is the recommended approach.

**File Naming Conventions:**

| File Path                 | URL Path              | Description                                                                                                         |
| :------------------------ | :-------------------- | :------------------------------------------------------------------------------------------------------------------ |
| `__root.tsx`              | (none)                | **Required.** The root layout for the entire app. Contains the main `<html>`, `<head>`, `<body>`, and `<Outlet />`. |
| `index.tsx`               | `/`                   | The home page, rendered inside the root layout's `<Outlet />`.                                                      |
| `about.tsx`               | `/about`              | A static route.                                                                                                     |
| `posts.tsx`               | `/posts`              | A **layout route**. Wraps all `/posts/*` URLs. Must render `<Outlet />`.                                            |
| `posts/index.tsx`         | `/posts`              | The index page for the `/posts` layout.                                                                             |
| `posts/$postId.tsx`       | `/posts/:postId`      | A **dynamic route**. `:postId` is a path parameter.                                                                 |
| `_auth.tsx`               | (none)                | A **pathless layout route**. Groups routes for logic (e.g., auth checks) without affecting the URL path.            |
| `_auth/dashboard.tsx`     | `/dashboard`          | A route grouped under the `_auth` pathless layout.                                                                  |
| `posts_.$postId.edit.tsx` | `/posts/:postId/edit` | A **non-nested route**. Renders _outside_ the `posts.tsx` layout.                                                   |
| `files/$.tsx`             | `/files/*`            | A **splat/catch-all route**. Matches any path after `/files/`.                                                      |

### **1.2. Defining a Route (`createFileRoute`)**

Each route file exports a `Route` constant defining its behavior.

**Comprehensive Route Definition Example (`/posts/$postId.tsx`):**

```tsx
import {
  createFileRoute,
  Outlet,
  Link,
  redirect,
  notFound,
} from "@tanstack/react-router";
import { z } from "zod";

// Define a Zod schema for search parameter validation
const postSearchSchema = z.object({
  version: z.number().int().positive().optional(),
});

// Define the route's context type, inherited from parent routes
interface PostRouteContext {
  auth: { isAuthenticated: boolean; username?: string };
}

async function fetchPost(postId: string) {
  /* ... */
}

export const Route = createFileRoute("/posts/$postId")({
  // Runs before the loader. Ideal for auth checks and redirects.
  beforeLoad: ({ context, location }) => {
    if (!context.auth.isAuthenticated) {
      throw redirect({ to: "/login", search: { redirect: location.href } });
    }
  },
  // Validates and types search params.
  validateSearch: (search) => postSearchSchema.parse(search),
  // Defines data dependencies. Re-runs loader if these change.
  loaderDeps: ({ search }) => ({ version: search.version }),
  // Fetches data for the route.
  loader: async ({ params }) => {
    const post = await fetchPost(params.postId);
    if (!post) throw notFound(); // Triggers notFoundComponent
    return { post };
  },
  // Component to render on success.
  component: PostComponent,
  // Optional: Component for pending state.
  pendingComponent: () => <div>Loading Post...</div>,
  // Optional: Component for errors.
  errorComponent: ({ error }) => (
    <div>Failed to load post: {error.message}</div>
  ),
  // Optional: Component for notFound() errors.
  notFoundComponent: () => <div>Post not found!</div>,
});

function PostComponent() {
  const { postId } = Route.useParams();
  const { version } = Route.useSearch();
  const { post } = Route.useLoaderData();
  const { auth } = Route.useRouteContext();

  return (
    <div>
      <h1>
        {post.title} (Version: {version ?? 1})
      </h1>
      <p>By {auth.username}</p>
      <p>{post.body}</p>
    </div>
  );
}
```

---

## **2. Tanstack Start: Full-Stack Features**

### **2.1. Server Functions (RPC)**

Execute server-only logic from the client. This is the primary mechanism for mutations.

**Defining a Server Function:**

```ts
// src/server/actions.ts
import { createServerFn } from "@tanstack/react-start";
import { z } from "zod";
import { db } from "~/server/db";

const updatePostSchema = z.object({
  postId: z.string(),
  content: z.string(),
});

export const updatePost = createServerFn({ method: "POST" })
  .validator(updatePostSchema) // Validates input at runtime, provides types
  .handler(async ({ data }) => {
    // `data` is typed from the validator
    // This code ONLY runs on the server
    return db.posts.update({ where: { id: data.postId }, data });
  });
```

**Calling a Server Function (Progressive Enhancement):**
Use the `.url` property for the `<form>` action to support non-JS clients. Use client-side handlers for an enhanced SPA experience.

```tsx
import { updatePost } from "~/server/actions";
import { useRouter } from "@tanstack/react-router";

function EditPostForm({ post }) {
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    await updatePost({
      data: {
        postId: post.id,
        content: formData.get("content") as string,
      },
    });
    router.invalidate(); // Invalidate router cache to refetch data
  };

  return (
    <form action={updatePost.url} method="POST" onSubmit={handleSubmit}>
      <input name="postId" value={post.id} type="hidden" />
      <textarea name="content" defaultValue={post.content}></textarea>
      <button type="submit">Update</button>
    </form>
  );
}
```

### **2.2. Server Routes (API Endpoints)**

Create traditional REST-like API endpoints, co-located with page routes.

**Example: `src/routes/api/users.ts`**

```ts
import { createServerFileRoute } from "@tanstack/react-start/server";
import { json } from "@tanstack/react-start"; // Helper for JSON responses

export const ServerRoute = createServerFileRoute("/api/users").methods({
  GET: async () => {
    const users = await db.users.findMany();
    return json(users); // Responds with Content-Type: application/json
  },
  POST: async ({ request }) => {
    const newUser = await request.json();
    const createdUser = await db.users.create({ data: newUser });
    return json(createdUser, { status: 201 });
  },
});
```

---

## **3. Core APIs and Hooks Reference**

### **3.1. Tanstack Router Hooks**

These hooks are used within your React components to interact with the router state. For best type-safety, use the hooks bound to your route object (e.g., `Route.useParams()`) or use `getRouteApi('route/path')` in code-split components.

| Hook                  | Purpose                                                              | Common Options    | Example                                                      |
| :-------------------- | :------------------------------------------------------------------- | :---------------- | :----------------------------------------------------------- |
| **`useParams`**       | Access dynamic path parameters (e.g., from `$postId`).               | `from`, `select`  | `const { postId } = Route.useParams()`                       |
| **`useSearch`**       | Access validated URL search/query parameters.                        | `from`, `select`  | `const { page, q } = Route.useSearch()`                      |
| **`useLoaderData`**   | Access data returned from the route's `loader` function.             | `from`, `select`  | `const { post } = Route.useLoaderData()`                     |
| **`useNavigate`**     | Get a function to perform imperative navigation.                     | `from`            | `const navigate = useNavigate(); navigate({ to: '/posts' })` |
| **`useRouter`**       | Access the global router instance.                                   | (none)            | `const router = useRouter(); router.invalidate()`            |
| **`useMatch`**        | Check if a route is currently active. Returns match info or `false`. | `from`, `pending` | `const isActive = useMatch({ from: Route.fullPath })`        |
| **`useBlocker`**      | Prevent navigation, e.g., for unsaved forms.                         | `shouldBlockFn`   | `useBlocker({ shouldBlockFn: () => isDirty })`               |
| **`useRouteContext`** | Access context provided by parent routes.                            | `from`, `select`  | `const { auth } = Route.useRouteContext()`                   |

**`getRouteApi(routeId)`**
Used in code-split components to get a type-safe API for a specific route without causing circular dependencies.

```tsx
import { getRouteApi } from "@tanstack/react-router";
const routeApi = getRouteApi("/posts/$postId");

function PostDetails() {
  const { post } = routeApi.useLoaderData();
  return <div>{post.title}</div>;
}
```

### **3.2. Tanstack Start APIs**

**`createServerFn(options)`**
The core factory for creating RPC-style server functions.

- **`options.method`: `'GET' | 'POST'`** - HTTP method. Use `POST` for actions with side-effects.
- **`options.response`: `'data' | 'full' | 'raw'`** - How the response is handled. `'data'` is default. `'raw'` is for streaming.
- **`.validator(schema)`**: A function or Zod-like schema to validate input. This provides runtime safety and infers the `data` type for the handler.
- **`.handler(async ({ data, context }) => { ... })`**: The server-only logic. `data` is the validated input.
- **`.middleware([...])`**: Apply middleware like authentication checks.

**`useServerFn(serverFn)`**
A client-side hook that wraps a server function. It automatically handles router-level concerns like redirects and `notFound` errors thrown by the server function.

```tsx
import { useServerFn } from "@tanstack/react-start";
import { someServerAction } from "~/server/actions";

function MyComponent() {
  const myAction = useServerFn(someServerAction);
  // myAction will now correctly trigger router redirects/not-founds
}
```

**Server Function Context Utilities**
Inside a server function's `.handler()`, you can access the raw request context.

- `getWebRequest()`: Returns the standard `Request` object.
- `getHeader(name)`: Reads a request header.
- `setHeader(name, value)`: Sets a response header.
- `setResponseStatus(code)`: Sets the response status code.

**`createServerFileRoute(path)`**
The factory for creating API routes.

- **`.methods({ GET, POST, ... })`**: Define handlers for different HTTP methods. Each handler receives `{ request, params, context }`.

### **3.3. Navigation APIs**

The `<Link>` component and `navigate` function share a common, type-safe API.

**`<Link>` Component Props:**

- **`to`: `string`** - The destination path. Fully type-safe and autocompletes.
- **`params`: `object | (prev) => object`** - Required for dynamic routes.
- **`search`: `object | (prev) => object`** - To set or update search params.
- **`from`: `string`** - The origin route path, enabling relative navigation (`to="."`) and better type inference.
- **`activeProps`: `{ style, className }`** - Props to apply when the link is active.
- **`preload`: `'intent' | 'render' | 'viewport'`** - Strategy for preloading route data. `'intent'` (on hover/touch) is a common default.
- **`mask`: `{ to, params, ... }`** - Displays a different URL in the address bar than the one being rendered, useful for modals.

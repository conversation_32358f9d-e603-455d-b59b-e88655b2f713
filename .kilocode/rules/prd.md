### **Product Requirements Document: Immersive AI Companion Chat**

#### **1. Overview**

This document outlines the requirements for an advanced, AI-powered chat application designed to provide users with immersive, personal, and persistent interactions with unique AI "Companions." Each companion will possess a distinct personality, backstory, and visual identity. The application will function as a platform for users to discover, befriend, and build relationships with these AI characters.

The core experience will be delivered through a web-based chat interface, stylized as a modern messenger. It will be a rich, multi-modal experience combining text, dynamically changing avatars, user-requested image generation, and voice capabilities. The system will be built on a robust architecture that supports user accounts, a token-based economy, and persistent chat history, fostering long-term engagement.

#### **2. Goals & Objectives**

- **Primary Goal:** To create a highly engaging and personalized companionship experience where users can form meaningful, ongoing connections with AI characters.
- **User Experience Goal:** To provide a seamless, multi-modal interface that combines text, visuals, and voice, making interactions feel lifelike and immersive.
- **Business Goal:** To build a sustainable business through a fair, token-based subscription and purchase model that rewards user engagement.
- **Retention Goal:** To foster long-term user retention through personalization, character memory, and proactive engagement features.
- **Technical Goal:** To build an efficient system that minimizes LLM costs where possible, while delivering a high-quality experience. This includes using a single, structured API call per turn to fetch all necessary data.

#### **3. Target Audience**

- **Primary:** Users seeking AI companionship, personal chat experiences, and interactive storytelling. This includes fans of TTRPGs, interactive fiction, and character-centric chat applications.
- **Secondary:** Casual users looking for a creative and unique AI-powered entertainment experience.
- **Consideration:** A significant portion of the target audience may have an interest in anime-styled characters and interactions in their native language.

#### **4. Core User Flow**

1.  **Onboarding:** A new user registers for a free account (e.g., via email verification) and receives a starting balance of "tokens."
2.  **Discovery:** The user browses a catalog of AI Companions, using filters (e.g., gender, personality tags like "Funny," "Adventurous") to find one that interests them. They can mark characters as favorites.
3.  **Initiation:** The user selects a companion and starts a new chat session. The chat history is persistent and will be loaded if it exists.
4.  **Interaction Loop (Turn-based):**
    a. **User Action:** The user takes an action by:
        i. Typing a custom message.
        ii. Sending a voice message (which is transcribed to text).
        iii. Clicking an AI-provided suggestion button.
        iv. Spending tokens to request a custom-generated image.
    b. **Backend Processing:** The frontend sends the user's action and the current chat history to the backend. The system deducts tokens for the message.
    c. **Single LLM Call:** The backend makes **one single call** to the LLM (e.g., Google Gemini), providing the chat history, character persona, and a system prompt instructing the model to return a structured JSON object.
    d. **Structured Response:** The LLM processes the context and returns a single JSON object containing all elements for the next turn.
    e. **Asynchronous Streaming & Rendering:**
        i. The backend immediately begins streaming the LLM's structured response to the frontend. The user sees the character's text response appearing.
        ii. The frontend updates the character's reactive avatar based on the mood/context received in the response.
        iii. The user can play an audio version of the character's response (Text-to-Speech).
        iv. **If** the user requested an image, the backend **simultaneously** calls the Image Generation API. When complete, the image URL is streamed and rendered inline.
        v. Suggestion buttons for the user's next action appear.
5.  **Session & Retention:**
    a. A chat session ends after a period of user inactivity.
    b. If a user is inactive for a longer period, the system can send a proactive notification (e.g., an email or push notification) from the character to re-engage the user.
    c. The interaction loop (Step 4) continues until the user runs out of tokens or ends the session.
6.  **Monetization:** When tokens are low, the user is prompted to purchase more via a subscription or one-time token packs.

#### **5. Functional Requirements (Features)**

**5.1. User & Profile Management**
- **User Accounts:** Users must be able to register, log in, and manage a profile. Registration requires email verification.
- **Token Wallet:** Each user has a wallet that stores their token balance. The UI must clearly display the current balance.
- **Chat History:** All conversations with companions must be saved and associated with the user's account, allowing them to be continued at any time.

**5.2. Character Discovery & Management**
- **Character Catalog:** A browsable gallery of all available AI Companions.
- **Filtering & Sorting:** Users can filter characters by tags, gender, personality traits, etc.
- **Favorites:** Users can mark companions as "favorites" for quick access.

**5.3. AI Companion Engine**
- **Persistent Persona & Memory:** The LLM shall act as the AI Companion, maintaining a consistent personality, style, and memory of past conversations across multiple sessions.
- **Deep Configuration:** Each character will be pre-configured with attributes that define their behavior:
    - Gender, age (18+), sexual preferences.
    - Personality traits (e.g., touchiness, vulgarity settings).
    - Relationship Complexity: A configurable difficulty for developing intimacy.
    - A pre-defined face/body model for consistent image generation.
- **Contextual Reactions:** Companions can react negatively (e.g., become "offended") and may temporarily end a conversation if certain boundaries are crossed, based on their personality.
- **Content Filtering:** The system will support adult content filters to limit or allow certain types of content (e.g., nudity), which can be a user-level setting.

**5.4. Multi-Modal Chat Interface**
- **Text Chat:** The core text-based interaction.
- **Voice Input (Speech-to-Text):** Users can send voice messages, which are transcribed into text before being sent to the LLM.
- **Voice Output (Text-to-Speech):** Users can play an audio clip of the companion's response. The voice will be unique and consistent for each companion.
- **Reactive Avatars:** The companion's avatar (a pre-generated, upper-body image) will change based on the context and mood of the conversation (e.g., happy, thoughtful, flirty).
- **On-Demand Image Generation:** Users can spend tokens to request a unique photo (or short video) from the companion based on the current chat context. Generation is gated by the relationship level and character settings.

**5.5. Monetization & Economy**
- **Token-Based System:** All interactions (sending messages, generating images) will cost a certain number of tokens.
- **Onboarding Bonus:** New users receive a free allotment of tokens upon registration.
- **Subscriptions:** Users can subscribe for a monthly fee to receive a recurring stipend of tokens (e.g., daily or monthly) at a better rate.
- **Token Packs:** Users can make one-time purchases of token packs.
- **Re-engagement Bonus:** Lapsed users who have run out of tokens may be offered a small number of free tokens to incentivize their return.

**5.6. Retention & Notifications**
- **Proactive Messages:** Companions can "initiate" a conversation by sending a notification (email, push) to the user after a period of inactivity, encouraging them to return. This is a user-configurable setting.

**5.7. Structured AI Responses**
- The LLM must generate a single, structured JSON object per turn, containing:
  - `characterResponse` (string): Dialogue spoken by the companion.
  - `narration` (string, optional): Internal thoughts or environmental descriptions.
  - `avatarMood` (string): A key (e.g., "happy," "sad," "blushing") for the frontend to select the correct pre-made avatar image.
  - `shouldGenerateImage` (boolean): Flag indicating if a user-requested image is being processed.
  - `imagePrompt` (string, optional): The prompt for the image generation API.
  - `userSuggestions` (array of strings): 2-3 actionable choices for the user.

#### **6. Technical Requirements & Architecture**

- **Framework:** TanStack Start with TanStack Router.
- **Backend:** Node.js, TypeScript.
- **Frontend:** React, TypeScript.
- **UI** shadcn/ui with Radix UI and Tailwind. motion (prev Framer Motion) for animations
- **Dev Experience** EsLint, Prettier, SpotlightJS, TanStack Router Devtools
- **Database:** A relational database (PostgreSQL) to store user data, character profiles, chat history, and token balances.
- **Authentication:** Better Auth node package.
- **AI Integration:** Vercel AI SDK (version 5 beta).
  - The backend will use `streamText` with a `tool` definition for structured JSON.
  - The response will be streamed using the AI SDK's UI Message Stream protocol to interleave LLM text with custom data parts (e.g., for images).
- **LLM Provider:** Google Gemini (`gemini-2.5-flash` or similar).
  - **Strategy:** Consider using a more powerful model for the initial, engaging part of a conversation and switching to a more cost-effective model for sustained chat, using the initial context to guide the cheaper model.
- **External APIs:**
  - **Image Generation:** An external API for generating images/videos.
  - **Text-to-Speech (TTS):** An API to convert character text responses into audio.
  - **Speech-to-Text (STT):** An API to transcribe user voice messages.

#### **7. Out of Scope (for Version 1.0)**

- **User-Created Characters:** The ability for users to define and create their own companions from scratch.
- **Group Chats:** Interactions involving more than one AI companion at a time.
- **Real-Time Voice Calls:** Fluid, synchronous voice conversations (as opposed to turn-based voice messages).
- **Advanced Gamification:** Leaderboards or complex, multi-stage modes like the "Flirting Mode" concept.
- **"Photo Together" Feature:** Merging user-uploaded photos with companion images.

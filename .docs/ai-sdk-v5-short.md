### **Vercel AI SDK v5**

Vercel AI SDK v5 is a complete architectural redesign of the toolkit for building AI applications. It introduces a more powerful and flexible protocol, a new message system, and enhanced capabilities for building complex agents and generative UIs.

### **Key Differences & New Features in v5**

This version introduces significant breaking changes from v2/v3/v4 to support modern AI capabilities.

1.  **New Protocol (Server-Sent Events - SSE):**

    - **Previous:** Custom streaming protocol.
    - **v5:** Uses the web-standard Server-Sent Events (SSE) protocol. This is more robust, easier to debug, and works across all platforms.

2.  **Message System Overhaul (`UIMessage` vs. `ModelMessage`):**

    - **Previous:** A single `Message` type was used for both UI and model communication.
    - **v5:** Introduces two distinct message types:
      - `UIMessage`: Represents the full conversation state for the UI, including all parts (text, tools, custom data), metadata, and UI state. **This is the format you should store.**
      - `ModelMessage`: A simplified version optimized for sending to the LLM API, stripped of UI-specific data.
    - **Action Required:** You must now explicitly convert messages before sending them to the model using `convertToModelMessages(messages)`.

3.  **Message Content Structure (`message.parts`):**

    - **Previous:** Message content was typically a single string (`message.content`).
    - **v5:** Content is now an array of typed parts (`message.parts`). This natively supports multi-modal content (text, images), tool invocations, and custom data parts in a structured, ordered way.

4.  **New `useChat` Architecture (`ChatStore`):**

    - The `useChat` hook is now backed by a more flexible `ChatStore`, which manages state, processes response streams, and can be shared across multiple `useChat` instances.

5.  **New Backend Response Helpers:**

    - **Previous:** `StreamingTextResponse`.
    - **v5:** `toUIMessageStreamResponse()` is the new primary helper. It creates an SSE response that streams `UIMessage` parts (text, tools, custom data, etc.) to the client. For simpler cases, `toTextStreamResponse()` is still available.

6.  **Agentic Control Primitives:**

    - New experimental features for building agents:
      - `prepareStep`: A callback to dynamically change model, tools, or force a tool choice for a specific step in a multi-step generation.
      - `stopWhen`: A function to define custom stopping conditions for an agent's run loop (e.g., `stopWhen: hasToolCall('toolName')`).

7.  **Type-Safe Custom Data & Metadata:**
    - **Message Metadata:** Attach type-safe, structured metadata to messages (e.g., token usage, response time).
    - **Data Parts:** Stream arbitrary, type-safe JSON data from the server to the client as part of a message, enabling dynamic UI components.

---

### **Core SDK Usage (`ai` package)**

The core functions provide a unified API for interacting with various LLMs.

#### **1. Generating Text & Streaming (`streamText`)**

`streamText` is the primary function for interactive use cases like chat. It returns a result object with streams and helper methods.

**Backend API Route Example (Next.js App Router):**

```typescript
// app/api/chat/route.ts
import { openai } from "@ai-sdk/openai";
import { convertToModelMessages, streamText, UIMessage } from "ai";

export const maxDuration = 30;

export async function POST(req: Request) {
  // `messages` are of type `UIMessage[]`
  const { messages }: { messages: UIMessage[] } = await req.json();

  const result = await streamText({
    model: openai("gpt-4o"),
    // Explicitly convert UI messages to the format the model expects
    messages: convertToModelMessages(messages),
  });

  // Use the new v5 response helper
  return result.toUIMessageStreamResponse();
}
```

#### **2. Generating Structured Data (`streamObject`)**

Stream a structured, type-safe JSON object that conforms to a Zod schema.

**Backend API Route Example:**

```typescript
// app/api/generate-recipe/route.ts
import { openai } from "@ai-sdk/openai";
import { streamObject } from "ai";
import { z } from "zod";

export const recipeSchema = z.object({
  recipe: z.object({
    name: z.string(),
    ingredients: z.array(z.object({ name: z.string(), amount: z.string() })),
    steps: z.array(z.string()),
  }),
});

export async function POST(req: Request) {
  const { prompt }: { prompt: string } = await req.json();

  const result = await streamObject({
    model: openai("gpt-4o"),
    schema: recipeSchema,
    prompt,
  });

  // Can be consumed by `useObject` on the client
  return result.toTextStreamResponse();
}
```

#### **3. Tool Calling**

Define tools with parameters and an optional `execute` function for server-side execution. Client-side tools are defined without `execute`.

**Backend with Server-Side Tool:**

```typescript
// app/api/chat-with-tools/route.ts
import { openai } from "@ai-sdk/openai";
import { convertToModelMessages, streamText, UIMessage } from "ai";
import { z } from "zod";

export async function POST(req: Request) {
  const { messages }: { messages: UIMessage[] } = await req.json();

  const result = await streamText({
    model: openai("gpt-4o"),
    messages: convertToModelMessages(messages),
    tools: {
      // Server-side tool with an execute function
      getWeather: {
        description: "Get the weather for a location",
        parameters: z.object({ city: z.string() }),
        execute: async ({ city }) => ({ weather: "sunny", temp: 75 }),
      },
      // Client-side tool (no execute function)
      askForConfirmation: {
        description: "Ask the user for confirmation.",
        parameters: z.object({ message: z.string() }),
      },
    },
    // Enable multi-step tool use on the server for auto-executing tools
    maxSteps: 5,
  });

  return result.toUIMessageStreamResponse();
}
```

#### **4. Other Core Functions**

- `generateText()` / `generateObject()`: Non-streaming versions of the above.
- `embed({ model, value })`: Generates a single embedding vector.
- `embedMany({ model, values })`: Generates embeddings for multiple values in a batch.
- `experimental_generateImage({ model, prompt })`: Generates an image.
- `experimental_transcribe({ model, audio })`: Transcribes audio.
- `experimental_generateSpeech({ model, text, voice })`: Generates speech from text.

---

### **UI Hooks (`@ai-sdk/react`, etc.)**

Framework-agnostic hooks for building UIs.

#### **1. `useChat`**

The primary hook for building chatbots.

**Client-side Component Example:**

```tsx
"use client";
import { useChat } from "@ai-sdk/react";

export default function Chat() {
  const { messages, input, handleInputChange, handleSubmit, addToolResult } =
    useChat({
      // Set maxSteps > 1 to enable automatic re-submission after server-side tools execute
      maxSteps: 5,
      // Handle client-side tool calls
      async onToolCall({ toolCall }) {
        if (toolCall.toolName === "getLocation") {
          return "San Francisco"; // Return result for automatic execution
        }
      },
    });

  return (
    <div>
      {messages.map((m) => (
        <div key={m.id} className="message">
          <strong>{m.role}: </strong>
          {/* v5: Render message content from the `parts` array */}
          {m.parts.map((part, index) => {
            if (part.type === "text") {
              return <span key={index}>{part.text}</span>;
            }
            if (part.type === "tool-invocation") {
              // Render UI for tool calls (e.g., a confirmation button)
              if (part.toolInvocation.toolName === "askForConfirmation") {
                return (
                  <div key={index}>
                    <p>{part.toolInvocation.args.message}</p>
                    <button
                      onClick={() =>
                        addToolResult({
                          toolCallId: part.toolInvocation.toolCallId,
                          result: "Confirmed by user.",
                        })
                      }
                    >
                      Confirm
                    </button>
                  </div>
                );
              }
              // Render result of executed tools
              return (
                <div key={index}>
                  Tool call: {part.toolInvocation.toolName}, Result:{" "}
                  {JSON.stringify(part.toolInvocation.result)}
                </div>
              );
            }
            return null;
          })}
        </div>
      ))}

      <form onSubmit={handleSubmit}>
        <input
          value={input}
          onChange={handleInputChange}
          placeholder="Say something..."
        />
      </form>
    </div>
  );
}
```

#### **2. `useObject`**

Consumes a streamed JSON object from an API endpoint using `streamObject`.

```tsx
"use client";
import { experimental_useObject as useObject } from "@ai-sdk/react";
import { recipeSchema } from "./api/generate-recipe/schema";

export default function RecipeGenerator() {
  const { object, submit } = useObject({
    api: "/api/generate-recipe",
    schema: recipeSchema,
  });

  return (
    <div>
      <button onClick={() => submit({ prompt: "a lasagna recipe" })}>
        Generate Recipe
      </button>
      {object?.recipe && <pre>{JSON.stringify(object.recipe, null, 2)}</pre>}
    </div>
  );
}
```

#### **3. Streaming Custom Data**

Stream arbitrary data alongside the main response.

**Backend (`createUIMessageStreamResponse`):**

```typescript
// ... inside api route
return createUIMessageStreamResponse({
  execute: ({ writer }) => {
    writer.write({
      type: "data-status",
      id: "1",
      data: { state: "RAG starting" },
    });

    const result = streamText({ model, messages });
    writer.merge(result.toUIMessageStream());

    // onFinish or after merge...
    writer.write({
      type: "data-status",
      id: "1",
      data: { state: "RAG complete" },
    });
  },
});
```

**Frontend (`useChat` with `dataPartSchemas`):**

```tsx
// ... inside component
const { messages } = useChat({
  chatStore: defaultChatStoreOptions({
    api: "/api/chat",
    dataPartSchemas: {
      status: z.object({ state: z.string() }),
    },
  }),
});

// Render the data part from the message.parts array
messages.map((m) =>
  m.parts
    .filter((p) => p.type === "data-status")
    .map((p, i) => <div key={i}>Status: {p.data.state}</div>)
);
```

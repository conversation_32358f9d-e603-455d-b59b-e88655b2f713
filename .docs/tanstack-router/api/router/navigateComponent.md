---
id: navigateComponent
title: Navigate component
---

The `Navigate` component is a component that can be used to navigate to a new location when rendered. This includes changes to the pathname, search params, hash, and location state. The underlying navigation will happen inside of a `useEffect` hook when successfully rendered.

## Navigate props

The `Navigate` component accepts the following props:

### `...options`

- Type: [`NavigateOptions`](../NavigateOptionsType.md)

## Navigate returns

- `null`

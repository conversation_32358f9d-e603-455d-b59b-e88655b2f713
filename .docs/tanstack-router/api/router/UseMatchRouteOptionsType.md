---
id: UseMatchRouteOptionsType
title: UseMatchRouteOptions type
---

The `UseMatchRouteOptions` type extends the [`ToOptions`](../ToOptionsType.md) type and describes additional options available when using the [`useMatchRoute`](../useMatchRouteHook.md) hook.

```tsx
export type UseMatchRouteOptions = ToOptions & MatchRouteOptions
```

- [`ToOptions`](../ToOptionsType.md)
- [`MatchRouteOptions`](../MatchRouteOptionsType.md)

---
id: router
title: Router API
---

- Functions
  - [`createFileRoute`](../router/createFileRouteFunction.md)
  - [`createLazyFileRoute`](../router/createLazyFileRouteFunction.md)
  - [`createRootRoute`](../router/createRootRouteFunction.md)
  - [`createRootRouteWithContext`](../router/createRootRouteWithContextFunction.md)
  - [`createRoute`](../router/createRouteFunction.md)
  - [`createLazyRoute`](../router/createLazyRouteFunction.md)
  - [`createRouteMask`](../router/createRouteMaskFunction.md)
  - [`createRouter`](../router/createRouterFunction.md)
  - [`defer`](../router/deferFunction.md)
  - [`getRouteApi`](../router/getRouteApiFunction.md)
  - [`isNotFound`](../router/isNotFoundFunction.md)
  - [`isRedirect`](../router/isRedirectFunction.md)
  - [`lazyRouteComponent`](../router/lazyRouteComponentFunction.md)
  - [`linkOptions`](../router/linkOptions.md)
  - [`notFound`](../router/notFoundFunction.md)
  - [`redirect`](../router/redirectFunction.md)
  - [`retainSearchParams`](../router/retainSearchParamsFunction.md)
  - [`stripSearchParams`](../router/stripSearchParamsFunction.md)
- Components
  - [`<Await>`](../router/awaitComponent.md)
  - [`<CatchBoundary>`](../router/catchBoundaryComponent.md)
  - [`<CatchNotFound>`](../router/catchNotFoundComponent.md)
  - [`<ClientOnly>`](../router/clientOnlyComponent.md)
  - [`<DefaultGlobalNotFound>`](../router/defaultGlobalNotFoundComponent.md)
  - [`<ErrorComponent>`](../router/errorComponentComponent.md)
  - [`<Link>`](../router/linkComponent.md)
  - [`<MatchRoute>`](../router/matchRouteComponent.md)
  - [`<Navigate>`](../router/navigateComponent.md)
  - [`<Outlet>`](../router/outletComponent.md)
- Hooks
  - [`useAwaited`](../router/useAwaitedHook.md)
  - [`useBlocker`](../router/useBlockerHook.md)
  - [`useCanGoBack`](../router//useCanGoBack.md)
  - [`useChildMatches`](../router/useChildMatchesHook.md)
  - [`useLinkProps`](../router/useLinkPropsHook.md)
  - [`useLoaderData`](../router/useLoaderDataHook.md)
  - [`useLoaderDeps`](../router/useLoaderDepsHook.md)
  - [`useLocation`](../router/useLocationHook.md)
  - [`useMatch`](../router/useMatchHook.md)
  - [`useMatchRoute`](../router/useMatchRouteHook.md)
  - [`useMatches`](../router/useMatchesHook.md)
  - [`useNavigate`](../router/useNavigateHook.md)
  - [`useParentMatches`](../router/useParentMatchesHook.md)
  - [`useParams`](../router/useParamsHook.md)
  - [`useRouteContext`](../router/useRouteContextHook.md)
  - [`useRouter`](../router/useRouterHook.md)
  - [`useRouterState`](../router/useRouterStateHook.md)
  - [`useSearch`](../router/useSearchHook.md)
- Types
  - [`ActiveLinkOptions Type`](../router/ActiveLinkOptionsType.md)
  - [`AsyncRouteComponent Type`](../router/AsyncRouteComponentType.md)
  - [`HistoryState Interface`](../router/historyStateInterface.md)
  - [`LinkOptions Type`](../router/LinkOptionsType.md)
  - [`LinkProps Type`](../router/LinkPropsType.md)
  - [`MatchRouteOptions Type`](../router/MatchRouteOptionsType.md)
  - [`NavigateOptions Type`](../router/NavigateOptionsType.md)
  - [`NotFoundError Type`](../router/NotFoundErrorType.md)
  - [`ParsedHistoryState Type`](../router/ParsedHistoryStateType.md)
  - [`ParsedLocation Type`](../router/ParsedLocationType.md)
  - [`Redirect Type`](../router/RedirectType.md)
  - [`Register Type`](../router/RegisterType.md)
  - [`Route Type`](../router/RouteType.md)
  - [`RouteApi Type`](../router/RouteApiType.md)
  - [`RouteMask Type`](../router/RouteMaskType.md)
  - [`RouteMatch Type`](../router/RouteMatchType.md)
  - [`RouteOptions Type`](../router/RouteOptionsType.md)
  - [`Router Type`](../router/RouterType.md)
  - [`RouterEvents Type`](../router/RouterEventsType.md)
  - [`RouterOptions Type`](../router/RouterOptionsType.md)
  - [`RouterState Type`](../router/RouterStateType.md)
  - [`ToMaskOptions Type`](../router/ToMaskOptionsType.md)
  - [`ToOptions Type`](../router/ToOptionsType.md)
  - [`UseMatchRouteOptions Type`](../router/UseMatchRouteOptionsType.md)
  - [`ViewTransitionOptions Type`](../router/ViewTransitionOptionsType.md)
- ⚠️ Deprecated
  - [`FileRoute Class`](../router/FileRouteClass.md)
  - [`Route Class`](../router/RouteClass.md)
  - [`Router Class`](../router/RouterClass.md)
  - [`RouteApi Class`](../router/RouteApiClass.md)
  - [`RootRoute Class`](../router/RootRouteClass.md)
  - [`NotFoundRoute Class`](../router/NotFoundRouteClass.md)
  - [`rootRouteWithContext Function`](../router/rootRouteWithContextFunction.md)

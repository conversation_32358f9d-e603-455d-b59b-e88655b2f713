{"files.watcherExclude": {"**/routeTree.gen.ts": true}, "search.exclude": {"**/routeTree.gen.ts": true}, "files.readonlyInclude": {"**/routeTree.gen.ts": true}, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "files.eol": "\n", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[graphql]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "biome.enabled": false}
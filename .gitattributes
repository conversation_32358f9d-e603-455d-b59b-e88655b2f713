# Set the default behavior, in case people don't have correct git settings
* text=auto eol=lf

# Don't allow people to merge changes to these generated files, because the result
# may be invalid.  You need to run "pnpm update" again.
pnpm-lock.yaml               merge=binary
shrinkwrap.yaml              merge=binary
npm-shrinkwrap.json          merge=binary
yarn.lock                    merge=binary
package-lock.json            merge=binary

*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.eot binary
*.ttf binary
*.woff binary
*.woff2 binary
*.webm binary


# Custom export-ignore for Git archive commands
.gitattributes export-ignore
.gitignore export-ignore
README.md export-ignore
LICENSE export-ignore

# Suppressing whitespace differences in certain file types
*.md -whitespace

# Use the union merge strategy for CHANGELOG files
CHANGELOG.md merge=union

# Marking generated files
pnpm-lock.yaml linguist-generated=true
package-lock.json linguist-generated=true
package-lock.json linguist-generated=true
yarn.lock linguist-generated=true

import { sentryVitePlugin } from '@sentry/vite-plugin';
import tailwindcss from '@tailwindcss/vite';
import { tanstackStart } from '@tanstack/react-start/plugin/vite';
import { defineConfig } from 'vite';
import tsConfigPaths from 'vite-tsconfig-paths';

export default defineConfig({
  build: {
    sourcemap: 'hidden',
  },
  server: {
    port: 3000,
  },
  plugins: [
    tsConfigPaths({
      projects: ['./tsconfig.json'],
    }),
    tanstackStart(),
    tailwindcss(),
    sentryVitePlugin({
      telemetry: false,
      reactComponentAnnotation: {
        enabled: true,
      },
      // org: process.env.SENTRY_ORG,
      // project: process.env.SENTRY_PROJECT,

      // // Auth tokens can be obtained from https://sentry.io/orgredirect/organizations/:orgslug/settings/auth-tokens/
      // authToken: process.env.SENTRY_AUTH_TOKEN,
    }),
  ],
});

import { type RefObject, type TextareaHTMLAttributes } from 'react';

import { cn } from '~/lib/utils';

interface AITextareaWrapperProps {
  className?: string;
  isLoading?: boolean;
}

export function AITextareaWrapper({ className, isLoading = false }: AITextareaWrapperProps) {
  return (
    <div
      className={cn(
        'rounded-3xl border border-[#444444] bg-[#1F2023] p-2 shadow-[0_8px_30px_rgba(0,0,0,0.24)] transition-all duration-300',
        isLoading && 'border-red-500/70',
        className,
      )}
    ></div>
  );
}

interface AITextareaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  className?: string;
}
const AITextarea = ({
  ref,
  className,
  ...props
}: AITextareaProps & { ref?: RefObject<HTMLTextAreaElement | null> }) => (
  <textarea
    className={cn(className,
      'scrollbar-thin scrollbar-thumb-[#444444] scrollbar-track-transparent hover:scrollbar-thumb-[#555555] flex min-h-[44px] w-full resize-none rounded-md border-none bg-transparent px-3 py-2.5 text-base text-gray-100 placeholder:text-gray-400 focus-visible:ring-0 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50',
    )}
    ref={ref}
    rows={1}
    {...props}
  />
);
AITextarea.displayName = 'AITextarea';

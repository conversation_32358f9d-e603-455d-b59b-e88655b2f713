'use client';

import { useEffect, useRef, useState } from 'react';

import type { StoryElements } from '~/lib/schemas';

export default function ChatUI() {
  // State for each piece of data
  const [narration, setNarration] = useState('');
  const [characterResponse, setCharacterResponse] = useState('');
  const [storyElements, setStoryElements] = useState<null | StoryElements>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<null | string>(null);

  // Ref to hold the EventSource instance
  const eventSourceReference = useRef<EventSource | null>(null);

  const handleActionClick = (prompt: string) => {
    // 1. Reset all state for the new request
    setNarration('');
    setCharacterResponse('');
    setStoryElements(null);
    setError(null);
    setIsLoading(true);

    // Close any existing connection
    if (eventSourceReference.current) {
      eventSourceReference.current.close();
    }

    // 2. Create a new EventSource to connect to our backend
    const source = new EventSource('/api/chat', {
        // EventSource doesn't support POST directly. We'll send the prompt via a POST
        // request that INITIATES the SSE connection on the server.
        // This is a common pattern.
    });
    eventSourceReference.current = source;

    // 3. Define event listeners for our custom events
    source.addEventListener('narration', (event) => {
      const textChunk = JSON.parse(event.data as string) as string;
      setNarration((previous) => previous + textChunk);
    });

    source.addEventListener('character', (event) => {
      const textChunk = JSON.parse(event.data as string) as string;
      setCharacterResponse((previous) => previous + textChunk);
    });

    source.addEventListener('data', (event) => {
      const data = JSON.parse(JSON.parse(event.data as string) as string) as StoryElements;
      setStoryElements(data);
    });

    source.addEventListener('end', (event) => {
      console.log('Stream ended:', event.data);
      setIsLoading(false);
      source.close(); // Clean up the connection
    });

    source.addEventListener('error', (event) => {
      console.error('EventSource failed:', event);
      setError('A streaming error occurred. Connection closed.');
      setIsLoading(false);
      source.close();
    });

    // This fetch initiates the POST request that the EventSource will listen to.
    fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: prompt }),
    }).catch(error_ => {
      console.error('Failed to initiate stream:', error_);
      if (error_ instanceof Error) {
        setError(`Failed to start the story stream: ${error_.message}`);
      }
      setIsLoading(false);
    });
  };

  // Clean up connection on component unmount
  useEffect(() => {
    return () => {
      if (eventSourceReference.current) {
        eventSourceReference.current.close();
      }
    };
  }, []);

  const initialPrompts = [
    "You enter a mysterious tavern.",
    "You find an ancient scroll in a cave.",
    "You wake up in a strange forest."
  ];

  const hasStarted = narration || characterResponse || isLoading;

  return (
    <main className="flex flex-col w-full max-w-3xl mx-auto p-4 md:p-8">
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 space-y-6">
        <h1 className="text-2xl font-bold text-center text-white">AI Storyteller (Backend Parsing)</h1>

        {/* Initial Prompts UI */}
        {!hasStarted && (
          <div className="space-y-4">
            <p className="text-gray-300 text-center">Choose a prompt to begin your adventure:</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {initialPrompts.map(prompt => (
                <button
                  className="bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700 transition-colors"
                  key={prompt}
                  onClick={() => { handleActionClick(prompt); }}
                >
                  {prompt}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Response Display */}
        {hasStarted && (
          <div className="space-y-4">
            {/* Narration Box */}
            <div className="bg-gray-900 p-4 rounded-lg">
              <h3 className="font-semibold text-purple-400 mb-2">Narration</h3>
              <p className="text-gray-300 italic whitespace-pre-wrap">{narration || '...'}</p>
            </div>

            {/* Character Response Box */}
            <div className="bg-gray-900 p-4 rounded-lg">
              <h3 className="font-semibold text-cyan-400 mb-2">Character Response</h3>
              <p className="text-gray-200 whitespace-pre-wrap">{characterResponse || '...'}</p>
            </div>

            {/* Structured Data Box */}
            {storyElements && (
              <div className="bg-gray-900 p-4 rounded-lg space-y-4">
                <div>
                  <h3 className="font-semibold text-yellow-400">Image Prompt:</h3>
                  <pre className="text-sm bg-black p-2 rounded-md text-gray-400 font-mono mt-1 overflow-x-auto">
                    {storyElements.imagePrompt}
                  </pre>
                </div>
              </div>
            )}

            {error && <p className="text-red-500 text-center">{error}</p>}
          </div>
        )}

        {/* User Action Buttons */}
        <div className="space-y-4 border-t border-gray-700 pt-4">
            <h3 className="font-semibold text-green-400 text-center">
              {isLoading ? 'Generating response...' : 'What do you do next?'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {storyElements?.userSuggestions.map((suggestion) => (
                    <button
                      className="bg-green-600 text-white p-3 rounded-lg hover:bg-green-700 transition-colors text-left disabled:bg-gray-600 disabled:cursor-not-allowed"
                      disabled={isLoading}
                      key={suggestion}
                      onClick={() => { handleActionClick(suggestion); }}
                    >
                      {suggestion}
                    </button>
                ))}
            </div>
          </div>
      </div>
    </main>
  );
}

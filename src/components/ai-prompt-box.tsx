import * as DialogPrimitive from '@radix-ui/react-dialog';
import * as TooltipPrimitive from '@radix-ui/react-tooltip';
import {
  ArrowUp,
  BrainCog,
  FolderCode,
  Globe,
  Mic,
  Paperclip,
  Square,
  StopCircle,
  X,
} from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import {
  ButtonHTMLAttributes,
  ComponentProps,
  ComponentPropsWithoutRef,
  ComponentRef,
  createContext,
  DragEvent,
  HTMLAttributes,
  KeyboardEvent,
  ReactNode,
  RefObject,
  TextareaHTMLAttributes,
  use,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { formatTime } from '~/lib/datetime';
import { cn } from '~/lib/utils';

// Embedded CSS for minimal custom styles
const styles = `
  *:focus-visible {
    outline-offset: 0 !important;
    --ring-offset: 0 !important;
  }
  textarea::-webkit-scrollbar {
    width: 6px;
  }
  textarea::-webkit-scrollbar-track {
    background: transparent;
  }
  textarea::-webkit-scrollbar-thumb {
    background-color: #444444;
    border-radius: 3px;
  }
  textarea::-webkit-scrollbar-thumb:hover {
    background-color: #555555;
  }
`;

// Inject styles into document
const styleSheet = document.createElement('style');
styleSheet.textContent = styles;
document.head.append(styleSheet);

// Textarea Component
interface TextareaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  className?: string;
}
const Textarea = ({
  ref,
  className,
  ...props
}: TextareaProps & { ref?: RefObject<HTMLTextAreaElement | null> }) => (
  <textarea
    className={cn(
      'scrollbar-thin scrollbar-thumb-[#444444] scrollbar-track-transparent hover:scrollbar-thumb-[#555555] flex min-h-[44px] w-full resize-none rounded-md border-none bg-transparent px-3 py-2.5 text-base text-gray-100 placeholder:text-gray-400 focus-visible:ring-0 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50',
      className,
    )}
    ref={ref}
    rows={1}
    {...props}
  />
);
Textarea.displayName = 'Textarea';

// Tooltip Components
const TooltipProvider = TooltipPrimitive.Provider;
const Tooltip = TooltipPrimitive.Root;
const TooltipTrigger = TooltipPrimitive.Trigger;
const TooltipContent = ({
  ref,
  className,
  sideOffset = 4,
  ...props
}: React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content> & {
  ref?: React.RefObject<ComponentRef<typeof TooltipPrimitive.Content> | null>;
}) => (
  <TooltipPrimitive.Content
    className={cn(
      'animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 overflow-hidden rounded-md border border-[#333333] bg-[#1F2023] px-3 py-1.5 text-sm text-white shadow-md',
      className,
    )}
    ref={ref}
    sideOffset={sideOffset}
    {...props}
  />
);
TooltipContent.displayName = TooltipPrimitive.Content.displayName;

// Dialog Components
const Dialog = DialogPrimitive.Root;
const DialogPortal = DialogPrimitive.Portal;
const DialogOverlay = ({
  ref,
  className,
  ...props
}: ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay> & {
  ref?: RefObject<ComponentRef<typeof DialogPrimitive.Overlay> | null>;
}) => (
  <DialogPrimitive.Overlay
    className={cn(
      'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/60 backdrop-blur-sm',
      className,
    )}
    ref={ref}
    {...props}
  />
);
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName;

const DialogContent = ({
  ref,
  className,
  children,
  ...props
}: ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & {
  ref?: RefObject<ComponentRef<typeof DialogPrimitive.Content> | null>;
}) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      className={cn(
        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[90vw] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-2xl border border-[#333333] bg-[#1F2023] p-0 shadow-xl duration-300 md:max-w-[800px]',
        className,
      )}
      ref={ref}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute top-4 right-4 z-10 rounded-full bg-[#2E3033]/80 p-2 transition-all hover:bg-[#2E3033]">
        <X className="h-5 w-5 text-gray-200 hover:text-white" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
);
DialogContent.displayName = DialogPrimitive.Content.displayName;

const DialogTitle = ({
  ref,
  className,
  ...props
}: ComponentPropsWithoutRef<typeof DialogPrimitive.Title> & {
  ref?: RefObject<ComponentRef<typeof DialogPrimitive.Title> | null>;
}) => (
  <DialogPrimitive.Title
    className={cn(
      'text-lg leading-none font-semibold tracking-tight text-gray-100',
      className,
    )}
    ref={ref}
    {...props}
  />
);
DialogTitle.displayName = DialogPrimitive.Title.displayName;

// Button Component
interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  size?: 'default' | 'icon' | 'lg' | 'sm';
  variant?: 'default' | 'ghost' | 'outline';
}
const Button = ({
  ref,
  className,
  variant = 'default',
  size = 'default',
  ...props
}: ButtonProps & { ref?: RefObject<HTMLButtonElement | null> }) => {
  const variantClasses = {
    default: 'bg-white hover:bg-white/80 text-black',
    outline: 'border border-[#444444] bg-transparent hover:bg-[#3A3A40]',
    ghost: 'bg-transparent hover:bg-[#3A3A40]',
  };
  const sizeClasses = {
    default: 'h-10 px-4 py-2',
    sm: 'h-8 px-3 text-sm',
    lg: 'h-12 px-6',
    icon: 'h-8 w-8 rounded-full aspect-[1/1]',
  };
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center font-medium transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50',
        variantClasses[variant],
        sizeClasses[size],
        className,
      )}
      ref={ref}
      type="button"
      {...props}
    />
  );
};
Button.displayName = 'Button';

// VoiceRecorder Component
interface VoiceRecorderProps {
  isRecording: boolean;
  onStartRecording: () => void;
  onStopRecording: (duration: number) => void;
  visualizerBars?: number;
}
const VoiceRecorder: React.FC<VoiceRecorderProps> = ({
  isRecording,
  onStartRecording,
  onStopRecording,
  visualizerBars = 32,
}) => {
  const [time, setTime] = useState(0);
  const timerReference = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isRecording) {
      onStartRecording();
      timerReference.current = setInterval(() => {
        setTime((t) => t + 1);
      }, 1000);
    } else {
      if (timerReference.current) {
        clearInterval(timerReference.current);
        timerReference.current = null;
      }
      onStopRecording(time);
      setTime(0);
    }
    return () => {
      if (timerReference.current) clearInterval(timerReference.current);
    };
  }, [isRecording, time, onStartRecording, onStopRecording]);

  return (
    <div
      className={cn(
        'flex w-full flex-col items-center justify-center py-3 transition-all duration-300',
        isRecording ? 'opacity-100' : 'h-0 opacity-0',
      )}
    >
      <div className="mb-3 flex items-center gap-2">
        <div className="h-2 w-2 animate-pulse rounded-full bg-red-500" />
        <span className="font-mono text-sm text-white/80">
          {formatTime(time)}
        </span>
      </div>
      <div className="flex h-10 w-full items-center justify-center gap-0.5 px-4">
        {Array.from({ length: visualizerBars }).map((_, index) => (
          <div
            className="w-0.5 animate-pulse rounded-full bg-white/50"
            key={index}
            style={{
              height: `${String(Math.max(15, Math.random() * 100))}%`,
              animationDelay: `${String(index * 0.05)}s`,
              animationDuration: `${String(0.5 + Math.random() * 0.5)}s`,
            }}
          />
        ))}
      </div>
    </div>
  );
};

// ImageViewDialog Component
interface ImageViewDialogProps {
  imageUrl: null | string;
  onClose: () => void;
}
const ImageViewDialog: React.FC<ImageViewDialogProps> = ({
  imageUrl,
  onClose,
}) => {
  if (!imageUrl) return null;
  return (
    <Dialog onOpenChange={onClose} open={!!imageUrl}>
      <DialogContent className="max-w-[90vw] border-none bg-transparent p-0 shadow-none md:max-w-[800px]">
        <DialogTitle className="sr-only">Image Preview</DialogTitle>
        <motion.div
          animate={{ opacity: 1, scale: 1 }}
          className="relative overflow-hidden rounded-2xl bg-[#1F2023] shadow-2xl"
          exit={{ opacity: 0, scale: 0.95 }}
          initial={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2, ease: 'easeOut' }}
        >
          <img
            alt="Full preview"
            className="max-h-[80vh] w-full rounded-2xl object-contain"
            src={imageUrl}
          />
        </motion.div>
      </DialogContent>
    </Dialog>
  );
};

// PromptInput Context and Components
interface PromptInputContextType {
  disabled?: boolean;
  isLoading: boolean;
  maxHeight: number | string;
  onSubmit?: () => void;
  setValue: (value: string) => void;
  value: string;
}
const PromptInputContext = createContext<PromptInputContextType>({
  isLoading: false,
  value: '',
  setValue: () => {
    // empty
  },
  maxHeight: 240,
  onSubmit: undefined,
  disabled: false,
});
function usePromptInput() {
  const context = use(PromptInputContext);
  // if (!context.value) {
  //   throw new Error('usePromptInput must be used within a PromptInput');
  // }
  return context;
}

interface PromptInputProps {
  children: ReactNode;
  className?: string;
  disabled?: boolean;
  isLoading?: boolean;
  maxHeight?: number | string;
  onDragLeave?: (event: DragEvent) => void;
  onDragOver?: (event: DragEvent) => void;
  onDrop?: (event: DragEvent) => void;
  onSubmit?: () => void;
  onValueChange?: (value: string) => void;
  value?: string;
}
const PromptInput = ({
  ref,
  className,
  isLoading = false,
  maxHeight = 240,
  value,
  onValueChange,
  onSubmit,
  children,
  disabled = false,
  onDragOver,
  onDragLeave,
  onDrop,
}: PromptInputProps & { ref?: React.RefObject<HTMLDivElement | null> }) => {
  const [internalValue, setInternalValue] = useState(value ?? '');
  const handleChange = useCallback(
    (newValue: string) => {
      setInternalValue(newValue);
      onValueChange?.(newValue);
    },
    [onValueChange],
  );

  const context = useMemo(
    () => ({
      isLoading,
      value: value ?? internalValue,
      setValue: onValueChange ?? handleChange,
      maxHeight,
      onSubmit,
      disabled,
    }),
    [
      isLoading,
      value,
      internalValue,
      onValueChange,
      handleChange,
      maxHeight,
      onSubmit,
      disabled,
    ],
  );

  return (
    <TooltipProvider>
      <PromptInputContext value={context}>
        <div
          className={cn(
            'rounded-3xl border border-[#444444] bg-[#1F2023] p-2 shadow-[0_8px_30px_rgba(0,0,0,0.24)] transition-all duration-300',
            isLoading && 'border-red-500/70',
            className,
          )}
          onDragLeave={onDragLeave}
          onDragOver={onDragOver}
          onDrop={onDrop}
          ref={ref}
        >
          {children}
        </div>
      </PromptInputContext>
    </TooltipProvider>
  );
};
PromptInput.displayName = 'PromptInput';

interface PromptInputTextareaProps {
  disableAutosize?: boolean;
  placeholder?: string;
}
const PromptInputTextarea: React.FC<
  PromptInputTextareaProps & React.ComponentProps<typeof Textarea>
> = ({
  className,
  onKeyDown,
  disableAutosize = false,
  placeholder,
  ...props
}) => {
  const { value, setValue, maxHeight, onSubmit, disabled } = usePromptInput();
  const textareaReference = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (disableAutosize || !textareaReference.current) return;
    textareaReference.current.style.height = 'auto';
    textareaReference.current.style.height =
      typeof maxHeight === 'number'
        ? `${String(
            Math.min(textareaReference.current.scrollHeight, maxHeight),
          )}px`
        : `min(${String(
            textareaReference.current.scrollHeight,
          )}px, ${maxHeight})`;
  }, [value, maxHeight, disableAutosize]);

  const handleKeyDown = (event: KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      onSubmit?.();
    }
    onKeyDown?.(event);
  };

  return (
    <Textarea
      className={cn('text-base', className)}
      disabled={disabled}
      onChange={(event) => {
        setValue(event.target.value);
      }}
      onKeyDown={handleKeyDown}
      placeholder={placeholder}
      ref={textareaReference}
      value={value}
      {...props}
    />
  );
};

type PromptInputActionsProps = HTMLAttributes<HTMLDivElement>;
const PromptInputActions = ({
  children,
  className,
  ...props
}: PromptInputActionsProps) => (
  <div className={cn('flex items-center gap-2', className)} {...props}>
    {children}
  </div>
);

interface PromptInputActionProps extends ComponentProps<typeof Tooltip> {
  children: ReactNode;
  className?: string;
  side?: 'bottom' | 'left' | 'right' | 'top';
  tooltip: ReactNode;
}
const PromptInputAction: React.FC<PromptInputActionProps> = ({
  tooltip,
  children,
  className,
  side = 'top',
  ...props
}) => {
  const { disabled } = usePromptInput();
  return (
    <Tooltip {...props}>
      <TooltipTrigger asChild disabled={disabled}>
        {children}
      </TooltipTrigger>
      <TooltipContent className={className} side={side}>
        {tooltip}
      </TooltipContent>
    </Tooltip>
  );
};

// Custom Divider Component
const CustomDivider: React.FC = () => (
  <div className="relative mx-1 h-6 w-[1.5px]">
    <div
      className="absolute inset-0 rounded-full bg-gradient-to-t from-transparent via-[#9b87f5]/70 to-transparent"
      style={{
        clipPath:
          'polygon(0% 0%, 100% 0%, 100% 40%, 140% 50%, 100% 60%, 100% 100%, 0% 100%, 0% 60%, -40% 50%, 0% 40%)',
      }}
    />
  </div>
);

// Main PromptInputBox Component
interface PromptInputBoxProps {
  className?: string;
  isLoading?: boolean;
  onSend?: (message: string, files?: File[]) => void;
  placeholder?: string;
}
export const PromptInputBox = ({
  ref,
  ...props
}: PromptInputBoxProps & { ref?: RefObject<HTMLDivElement | null> }) => {
  const {
    onSend = () => {
      // empty
    },
    isLoading = false,
    placeholder = 'Type your message here...',
    className,
  } = props;
  const [input, setInput] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const [filePreviews, setFilePreviews] = useState<Record<string, string>>({});
  const [selectedImage, setSelectedImage] = useState<null | string>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [showThink, setShowThink] = useState(false);
  const [showCanvas, setShowCanvas] = useState(false);
  const uploadInputReference = useRef<HTMLInputElement>(null);
  const promptBoxReference = useRef<HTMLDivElement>(null);

  const handleToggleChange = (value: string) => {
    if (value === 'search') {
      setShowSearch((previous) => !previous);
      setShowThink(false);
    } else if (value === 'think') {
      setShowThink((previous) => !previous);
      setShowSearch(false);
    }
  };

  const handleCanvasToggle = () => {
    setShowCanvas((previous) => !previous);
  };

  const isImageFile = (file: File) => file.type.startsWith('image/');

  const processFile = (file: File) => {
    if (!isImageFile(file)) {
      console.log('Only image files are allowed');
      return;
    }
    if (file.size > 10 * 1024 * 1024) {
      console.log('File too large (max 10MB)');
      return;
    }
    setFiles([file]);
    const reader = new FileReader();
    reader.addEventListener('load', (event) => {
      setFilePreviews({ [file.name]: event.target?.result as string });
    });
    reader.readAsDataURL(file);
  };

  const handleDragOver = useCallback((event: DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
  }, []);

  const handleDragLeave = useCallback((event: DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
  }, []);

  const handleDrop = useCallback((event: DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    const files = [...event.dataTransfer.files];
    const imageFiles = files.filter((file) => isImageFile(file));
    if (imageFiles.length > 0) processFile(imageFiles[0]);
  }, []);

  const handleRemoveFile = (index: number) => {
    const fileToRemove = files[index];
    if (filePreviews[fileToRemove.name]) {
      setFilePreviews({});
    }
    setFiles([]);
  };

  const openImageModal = (imageUrl: string) => {
    setSelectedImage(imageUrl);
  };

  const handlePaste = useCallback((event: ClipboardEvent) => {
    const items = event.clipboardData?.items;
    if (!items) return;
    for (const item of items) {
      if (item.type.includes('image')) {
        const file = item.getAsFile();
        if (file) {
          event.preventDefault();
          processFile(file);
          break;
        }
      }
    }
  }, []);

  useEffect(() => {
    document.addEventListener('paste', handlePaste);
    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, [handlePaste]);

  const handleSubmit = () => {
    if (input.trim() || files.length > 0) {
      let messagePrefix = '';
      if (showSearch) messagePrefix = '[Search: ';
      else if (showThink) messagePrefix = '[Think: ';
      else if (showCanvas) messagePrefix = '[Canvas: ';
      const formattedInput = messagePrefix
        ? `${messagePrefix}${input}]`
        : input;
      onSend(formattedInput, files);
      setInput('');
      setFiles([]);
      setFilePreviews({});
    }
  };

  // eslint-disable-next-line unicorn/consistent-function-scoping
  const handleStartRecording = () => {
    console.log('Started recording');
  };

  const handleStopRecording = (duration: number) => {
    console.log(`Stopped recording after ${String(duration)} seconds`);
    setIsRecording(false);
    onSend(`[Voice message - ${String(duration)} seconds]`, []);
  };

  const hasContent = input.trim() !== '' || files.length > 0;

  return (
    <>
      <PromptInput
        className={cn(
          'w-full border-[#444444] bg-[#1F2023] shadow-[0_8px_30px_rgba(0,0,0,0.24)] transition-all duration-300 ease-in-out',
          isRecording && 'border-red-500/70',
          className,
        )}
        disabled={isLoading || isRecording}
        isLoading={isLoading}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onSubmit={handleSubmit}
        onValueChange={setInput}
        ref={ref ?? promptBoxReference}
        value={input}
      >
        {files.length > 0 && !isRecording && (
          <div className="flex flex-wrap gap-2 p-0 pb-1 transition-all duration-300">
            {files.map((file, index) => (
              <div className="group relative" key={index}>
                {file.type.startsWith('image/') && filePreviews[file.name] && (
                  <div
                    className="h-16 w-16 cursor-pointer overflow-hidden rounded-xl transition-all duration-300"
                    onClick={() => {
                      openImageModal(filePreviews[file.name]);
                    }}
                  >
                    <img
                      alt={file.name}
                      className="h-full w-full object-cover"
                      src={filePreviews[file.name]}
                    />
                    <button
                      className="absolute top-1 right-1 rounded-full bg-black/70 p-0.5 opacity-100 transition-opacity"
                      onClick={(event) => {
                        event.stopPropagation();
                        handleRemoveFile(index);
                      }}
                      type="button"
                    >
                      <X className="h-3 w-3 text-white" />
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        <div
          className={cn(
            'transition-all duration-300',
            isRecording ? 'h-0 overflow-hidden opacity-0' : 'opacity-100',
          )}
        >
          <PromptInputTextarea
            className="text-base"
            placeholder={
              showSearch
                ? 'Search the web...'
                : showThink
                  ? 'Think deeply...'
                  : showCanvas
                    ? 'Create on canvas...'
                    : placeholder
            }
          />
        </div>

        {isRecording && (
          <VoiceRecorder
            isRecording={isRecording}
            onStartRecording={handleStartRecording}
            onStopRecording={handleStopRecording}
          />
        )}

        <PromptInputActions className="flex items-center justify-between gap-2 p-0 pt-2">
          <div
            className={cn(
              'flex items-center gap-1 transition-opacity duration-300',
              isRecording ? 'invisible h-0 opacity-0' : 'visible opacity-100',
            )}
          >
            <PromptInputAction tooltip="Upload image">
              <button
                className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full text-[#9CA3AF] transition-colors hover:bg-gray-600/30 hover:text-[#D1D5DB]"
                disabled={isRecording}
                onClick={() => uploadInputReference.current?.click()}
                type="button"
              >
                <Paperclip className="h-5 w-5 transition-colors" />
                <input
                  accept="image/*"
                  className="hidden"
                  onChange={(event) => {
                    if (event.target.files && event.target.files.length > 0) {
                      processFile(event.target.files[0]);
                    }
                    event.target.value = '';
                  }}
                  ref={uploadInputReference}
                  type="file"
                />
              </button>
            </PromptInputAction>

            <div className="flex items-center">
              <button
                className={cn(
                  'flex h-8 items-center gap-1 rounded-full border px-2 py-1 transition-all',
                  showSearch
                    ? 'border-[#1EAEDB] bg-[#1EAEDB]/15 text-[#1EAEDB]'
                    : 'border-transparent bg-transparent text-[#9CA3AF] hover:text-[#D1D5DB]',
                )}
                onClick={() => {
                  handleToggleChange('search');
                }}
                type="button"
              >
                <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center">
                  <motion.div
                    animate={{
                      rotate: showSearch ? 360 : 0,
                      scale: showSearch ? 1.1 : 1,
                    }}
                    transition={{
                      type: 'spring',
                      stiffness: 260,
                      damping: 25,
                    }}
                    whileHover={{
                      rotate: showSearch ? 360 : 15,
                      scale: 1.1,
                      transition: {
                        type: 'spring',
                        stiffness: 300,
                        damping: 10,
                      },
                    }}
                  >
                    <Globe
                      className={cn(
                        'h-4 w-4',
                        showSearch ? 'text-[#1EAEDB]' : 'text-inherit',
                      )}
                    />
                  </motion.div>
                </div>
                <AnimatePresence>
                  {showSearch && (
                    <motion.span
                      animate={{ width: 'auto', opacity: 1 }}
                      className="flex-shrink-0 overflow-hidden text-xs whitespace-nowrap text-[#1EAEDB]"
                      exit={{ width: 0, opacity: 0 }}
                      initial={{ width: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      Search
                    </motion.span>
                  )}
                </AnimatePresence>
              </button>

              <CustomDivider />

              <button
                className={cn(
                  'flex h-8 items-center gap-1 rounded-full border px-2 py-1 transition-all',
                  showThink
                    ? 'border-[#8B5CF6] bg-[#8B5CF6]/15 text-[#8B5CF6]'
                    : 'border-transparent bg-transparent text-[#9CA3AF] hover:text-[#D1D5DB]',
                )}
                onClick={() => {
                  handleToggleChange('think');
                }}
                type="button"
              >
                <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center">
                  <motion.div
                    animate={{
                      rotate: showThink ? 360 : 0,
                      scale: showThink ? 1.1 : 1,
                    }}
                    transition={{
                      type: 'spring',
                      stiffness: 260,
                      damping: 25,
                    }}
                    whileHover={{
                      rotate: showThink ? 360 : 15,
                      scale: 1.1,
                      transition: {
                        type: 'spring',
                        stiffness: 300,
                        damping: 10,
                      },
                    }}
                  >
                    <BrainCog
                      className={cn(
                        'h-4 w-4',
                        showThink ? 'text-[#8B5CF6]' : 'text-inherit',
                      )}
                    />
                  </motion.div>
                </div>
                <AnimatePresence>
                  {showThink && (
                    <motion.span
                      animate={{ width: 'auto', opacity: 1 }}
                      className="flex-shrink-0 overflow-hidden text-xs whitespace-nowrap text-[#8B5CF6]"
                      exit={{ width: 0, opacity: 0 }}
                      initial={{ width: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      Think
                    </motion.span>
                  )}
                </AnimatePresence>
              </button>

              <CustomDivider />

              <button
                className={cn(
                  'flex h-8 items-center gap-1 rounded-full border px-2 py-1 transition-all',
                  showCanvas
                    ? 'border-[#F97316] bg-[#F97316]/15 text-[#F97316]'
                    : 'border-transparent bg-transparent text-[#9CA3AF] hover:text-[#D1D5DB]',
                )}
                onClick={handleCanvasToggle}
                type="button"
              >
                <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center">
                  <motion.div
                    animate={{
                      rotate: showCanvas ? 360 : 0,
                      scale: showCanvas ? 1.1 : 1,
                    }}
                    transition={{
                      type: 'spring',
                      stiffness: 260,
                      damping: 25,
                    }}
                    whileHover={{
                      rotate: showCanvas ? 360 : 15,
                      scale: 1.1,
                      transition: {
                        type: 'spring',
                        stiffness: 300,
                        damping: 10,
                      },
                    }}
                  >
                    <FolderCode
                      className={cn(
                        'h-4 w-4',
                        showCanvas ? 'text-[#F97316]' : 'text-inherit',
                      )}
                    />
                  </motion.div>
                </div>
                <AnimatePresence>
                  {showCanvas && (
                    <motion.span
                      animate={{ width: 'auto', opacity: 1 }}
                      className="flex-shrink-0 overflow-hidden text-xs whitespace-nowrap text-[#F97316]"
                      exit={{ width: 0, opacity: 0 }}
                      initial={{ width: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      Canvas
                    </motion.span>
                  )}
                </AnimatePresence>
              </button>
            </div>
          </div>

          <PromptInputAction
            tooltip={
              isLoading
                ? 'Stop generation'
                : isRecording
                  ? 'Stop recording'
                  : hasContent
                    ? 'Send message'
                    : 'Voice message'
            }
          >
            <Button
              className={cn(
                'h-8 w-8 rounded-full transition-all duration-200',
                isRecording
                  ? 'bg-transparent text-red-500 hover:bg-gray-600/30 hover:text-red-400'
                  : hasContent
                    ? 'bg-white text-[#1F2023] hover:bg-white/80'
                    : 'bg-transparent text-[#9CA3AF] hover:bg-gray-600/30 hover:text-[#D1D5DB]',
              )}
              disabled={isLoading && !hasContent}
              onClick={() => {
                if (isRecording) setIsRecording(false);
                else if (hasContent) handleSubmit();
                else setIsRecording(true);
              }}
              size="icon"
              variant="default"
            >
              {isLoading ? (
                <Square className="h-4 w-4 animate-pulse fill-[#1F2023]" />
              ) : isRecording ? (
                <StopCircle className="h-5 w-5 text-red-500" />
              ) : hasContent ? (
                <ArrowUp className="h-4 w-4 text-[#1F2023]" />
              ) : (
                <Mic className="h-5 w-5 text-[#1F2023] transition-colors" />
              )}
            </Button>
          </PromptInputAction>
        </PromptInputActions>
      </PromptInput>

      <ImageViewDialog
        imageUrl={selectedImage}
        onClose={() => {
          setSelectedImage(null);
        }}
      />
    </>
  );
};
PromptInputBox.displayName = 'PromptInputBox';

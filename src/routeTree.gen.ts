/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createServerRootRoute } from '@tanstack/react-start/server'

import { Route as rootRouteImport } from './routes/__root'
import { Route as RedirectRouteImport } from './routes/redirect'
import { Route as DeferredRouteImport } from './routes/deferred'
import { Route as ChatRouteImport } from './routes/chat'
import { Route as PathlessLayoutRouteImport } from './routes/_pathlessLayout'
import { Route as UsersRouteRouteImport } from './routes/users.route'
import { Route as PostsRouteRouteImport } from './routes/posts.route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as UsersIndexRouteImport } from './routes/users.index'
import { Route as PostsIndexRouteImport } from './routes/posts.index'
import { Route as UsersUserIdRouteImport } from './routes/users.$userId'
import { Route as PostsPostIdRouteImport } from './routes/posts.$postId'
import { Route as PathlessLayoutNestedLayoutRouteImport } from './routes/_pathlessLayout/_nested-layout'
import { Route as PostsPostIdDeepRouteImport } from './routes/posts_.$postId.deep'
import { Route as PathlessLayoutNestedLayoutRouteBRouteImport } from './routes/_pathlessLayout/_nested-layout/route-b'
import { Route as PathlessLayoutNestedLayoutRouteARouteImport } from './routes/_pathlessLayout/_nested-layout/route-a'
import { ServerRoute as ApiUsersServerRouteImport } from './routes/api.users'
import { ServerRoute as ApiChatTestServerRouteImport } from './routes/api/chat-test'
import { ServerRoute as ApiChatServerRouteImport } from './routes/api/chat'
import { ServerRoute as ApiUsersIdServerRouteImport } from './routes/api/users.$id'

const rootServerRouteImport = createServerRootRoute()

const RedirectRoute = RedirectRouteImport.update({
  id: '/redirect',
  path: '/redirect',
  getParentRoute: () => rootRouteImport,
} as any)
const DeferredRoute = DeferredRouteImport.update({
  id: '/deferred',
  path: '/deferred',
  getParentRoute: () => rootRouteImport,
} as any)
const ChatRoute = ChatRouteImport.update({
  id: '/chat',
  path: '/chat',
  getParentRoute: () => rootRouteImport,
} as any)
const PathlessLayoutRoute = PathlessLayoutRouteImport.update({
  id: '/_pathlessLayout',
  getParentRoute: () => rootRouteImport,
} as any)
const UsersRouteRoute = UsersRouteRouteImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => rootRouteImport,
} as any)
const PostsRouteRoute = PostsRouteRouteImport.update({
  id: '/posts',
  path: '/posts',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const UsersIndexRoute = UsersIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => UsersRouteRoute,
} as any)
const PostsIndexRoute = PostsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => PostsRouteRoute,
} as any)
const UsersUserIdRoute = UsersUserIdRouteImport.update({
  id: '/$userId',
  path: '/$userId',
  getParentRoute: () => UsersRouteRoute,
} as any)
const PostsPostIdRoute = PostsPostIdRouteImport.update({
  id: '/$postId',
  path: '/$postId',
  getParentRoute: () => PostsRouteRoute,
} as any)
const PathlessLayoutNestedLayoutRoute =
  PathlessLayoutNestedLayoutRouteImport.update({
    id: '/_nested-layout',
    getParentRoute: () => PathlessLayoutRoute,
  } as any)
const PostsPostIdDeepRoute = PostsPostIdDeepRouteImport.update({
  id: '/posts_/$postId/deep',
  path: '/posts/$postId/deep',
  getParentRoute: () => rootRouteImport,
} as any)
const PathlessLayoutNestedLayoutRouteBRoute =
  PathlessLayoutNestedLayoutRouteBRouteImport.update({
    id: '/route-b',
    path: '/route-b',
    getParentRoute: () => PathlessLayoutNestedLayoutRoute,
  } as any)
const PathlessLayoutNestedLayoutRouteARoute =
  PathlessLayoutNestedLayoutRouteARouteImport.update({
    id: '/route-a',
    path: '/route-a',
    getParentRoute: () => PathlessLayoutNestedLayoutRoute,
  } as any)
const ApiUsersServerRoute = ApiUsersServerRouteImport.update({
  id: '/api/users',
  path: '/api/users',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiChatTestServerRoute = ApiChatTestServerRouteImport.update({
  id: '/api/chat-test',
  path: '/api/chat-test',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiChatServerRoute = ApiChatServerRouteImport.update({
  id: '/api/chat',
  path: '/api/chat',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiUsersIdServerRoute = ApiUsersIdServerRouteImport.update({
  id: '/$id',
  path: '/$id',
  getParentRoute: () => ApiUsersServerRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/posts': typeof PostsRouteRouteWithChildren
  '/users': typeof UsersRouteRouteWithChildren
  '/chat': typeof ChatRoute
  '/deferred': typeof DeferredRoute
  '/redirect': typeof RedirectRoute
  '/posts/$postId': typeof PostsPostIdRoute
  '/users/$userId': typeof UsersUserIdRoute
  '/posts/': typeof PostsIndexRoute
  '/users/': typeof UsersIndexRoute
  '/route-a': typeof PathlessLayoutNestedLayoutRouteARoute
  '/route-b': typeof PathlessLayoutNestedLayoutRouteBRoute
  '/posts/$postId/deep': typeof PostsPostIdDeepRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/chat': typeof ChatRoute
  '/deferred': typeof DeferredRoute
  '/redirect': typeof RedirectRoute
  '/posts/$postId': typeof PostsPostIdRoute
  '/users/$userId': typeof UsersUserIdRoute
  '/posts': typeof PostsIndexRoute
  '/users': typeof UsersIndexRoute
  '/route-a': typeof PathlessLayoutNestedLayoutRouteARoute
  '/route-b': typeof PathlessLayoutNestedLayoutRouteBRoute
  '/posts/$postId/deep': typeof PostsPostIdDeepRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/posts': typeof PostsRouteRouteWithChildren
  '/users': typeof UsersRouteRouteWithChildren
  '/_pathlessLayout': typeof PathlessLayoutRouteWithChildren
  '/chat': typeof ChatRoute
  '/deferred': typeof DeferredRoute
  '/redirect': typeof RedirectRoute
  '/_pathlessLayout/_nested-layout': typeof PathlessLayoutNestedLayoutRouteWithChildren
  '/posts/$postId': typeof PostsPostIdRoute
  '/users/$userId': typeof UsersUserIdRoute
  '/posts/': typeof PostsIndexRoute
  '/users/': typeof UsersIndexRoute
  '/_pathlessLayout/_nested-layout/route-a': typeof PathlessLayoutNestedLayoutRouteARoute
  '/_pathlessLayout/_nested-layout/route-b': typeof PathlessLayoutNestedLayoutRouteBRoute
  '/posts_/$postId/deep': typeof PostsPostIdDeepRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/posts'
    | '/users'
    | '/chat'
    | '/deferred'
    | '/redirect'
    | '/posts/$postId'
    | '/users/$userId'
    | '/posts/'
    | '/users/'
    | '/route-a'
    | '/route-b'
    | '/posts/$postId/deep'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/chat'
    | '/deferred'
    | '/redirect'
    | '/posts/$postId'
    | '/users/$userId'
    | '/posts'
    | '/users'
    | '/route-a'
    | '/route-b'
    | '/posts/$postId/deep'
  id:
    | '__root__'
    | '/'
    | '/posts'
    | '/users'
    | '/_pathlessLayout'
    | '/chat'
    | '/deferred'
    | '/redirect'
    | '/_pathlessLayout/_nested-layout'
    | '/posts/$postId'
    | '/users/$userId'
    | '/posts/'
    | '/users/'
    | '/_pathlessLayout/_nested-layout/route-a'
    | '/_pathlessLayout/_nested-layout/route-b'
    | '/posts_/$postId/deep'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  PostsRouteRoute: typeof PostsRouteRouteWithChildren
  UsersRouteRoute: typeof UsersRouteRouteWithChildren
  PathlessLayoutRoute: typeof PathlessLayoutRouteWithChildren
  ChatRoute: typeof ChatRoute
  DeferredRoute: typeof DeferredRoute
  RedirectRoute: typeof RedirectRoute
  PostsPostIdDeepRoute: typeof PostsPostIdDeepRoute
}
export interface FileServerRoutesByFullPath {
  '/api/chat': typeof ApiChatServerRoute
  '/api/chat-test': typeof ApiChatTestServerRoute
  '/api/users': typeof ApiUsersServerRouteWithChildren
  '/api/users/$id': typeof ApiUsersIdServerRoute
}
export interface FileServerRoutesByTo {
  '/api/chat': typeof ApiChatServerRoute
  '/api/chat-test': typeof ApiChatTestServerRoute
  '/api/users': typeof ApiUsersServerRouteWithChildren
  '/api/users/$id': typeof ApiUsersIdServerRoute
}
export interface FileServerRoutesById {
  __root__: typeof rootServerRouteImport
  '/api/chat': typeof ApiChatServerRoute
  '/api/chat-test': typeof ApiChatTestServerRoute
  '/api/users': typeof ApiUsersServerRouteWithChildren
  '/api/users/$id': typeof ApiUsersIdServerRoute
}
export interface FileServerRouteTypes {
  fileServerRoutesByFullPath: FileServerRoutesByFullPath
  fullPaths: '/api/chat' | '/api/chat-test' | '/api/users' | '/api/users/$id'
  fileServerRoutesByTo: FileServerRoutesByTo
  to: '/api/chat' | '/api/chat-test' | '/api/users' | '/api/users/$id'
  id:
    | '__root__'
    | '/api/chat'
    | '/api/chat-test'
    | '/api/users'
    | '/api/users/$id'
  fileServerRoutesById: FileServerRoutesById
}
export interface RootServerRouteChildren {
  ApiChatServerRoute: typeof ApiChatServerRoute
  ApiChatTestServerRoute: typeof ApiChatTestServerRoute
  ApiUsersServerRoute: typeof ApiUsersServerRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/redirect': {
      id: '/redirect'
      path: '/redirect'
      fullPath: '/redirect'
      preLoaderRoute: typeof RedirectRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/deferred': {
      id: '/deferred'
      path: '/deferred'
      fullPath: '/deferred'
      preLoaderRoute: typeof DeferredRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/chat': {
      id: '/chat'
      path: '/chat'
      fullPath: '/chat'
      preLoaderRoute: typeof ChatRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_pathlessLayout': {
      id: '/_pathlessLayout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof PathlessLayoutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/users': {
      id: '/users'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof UsersRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/posts': {
      id: '/posts'
      path: '/posts'
      fullPath: '/posts'
      preLoaderRoute: typeof PostsRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/users/': {
      id: '/users/'
      path: '/'
      fullPath: '/users/'
      preLoaderRoute: typeof UsersIndexRouteImport
      parentRoute: typeof UsersRouteRoute
    }
    '/posts/': {
      id: '/posts/'
      path: '/'
      fullPath: '/posts/'
      preLoaderRoute: typeof PostsIndexRouteImport
      parentRoute: typeof PostsRouteRoute
    }
    '/users/$userId': {
      id: '/users/$userId'
      path: '/$userId'
      fullPath: '/users/$userId'
      preLoaderRoute: typeof UsersUserIdRouteImport
      parentRoute: typeof UsersRouteRoute
    }
    '/posts/$postId': {
      id: '/posts/$postId'
      path: '/$postId'
      fullPath: '/posts/$postId'
      preLoaderRoute: typeof PostsPostIdRouteImport
      parentRoute: typeof PostsRouteRoute
    }
    '/_pathlessLayout/_nested-layout': {
      id: '/_pathlessLayout/_nested-layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof PathlessLayoutNestedLayoutRouteImport
      parentRoute: typeof PathlessLayoutRoute
    }
    '/posts_/$postId/deep': {
      id: '/posts_/$postId/deep'
      path: '/posts/$postId/deep'
      fullPath: '/posts/$postId/deep'
      preLoaderRoute: typeof PostsPostIdDeepRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_pathlessLayout/_nested-layout/route-b': {
      id: '/_pathlessLayout/_nested-layout/route-b'
      path: '/route-b'
      fullPath: '/route-b'
      preLoaderRoute: typeof PathlessLayoutNestedLayoutRouteBRouteImport
      parentRoute: typeof PathlessLayoutNestedLayoutRoute
    }
    '/_pathlessLayout/_nested-layout/route-a': {
      id: '/_pathlessLayout/_nested-layout/route-a'
      path: '/route-a'
      fullPath: '/route-a'
      preLoaderRoute: typeof PathlessLayoutNestedLayoutRouteARouteImport
      parentRoute: typeof PathlessLayoutNestedLayoutRoute
    }
  }
}
declare module '@tanstack/react-start/server' {
  interface ServerFileRoutesByPath {
    '/api/users': {
      id: '/api/users'
      path: '/api/users'
      fullPath: '/api/users'
      preLoaderRoute: typeof ApiUsersServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/chat-test': {
      id: '/api/chat-test'
      path: '/api/chat-test'
      fullPath: '/api/chat-test'
      preLoaderRoute: typeof ApiChatTestServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/chat': {
      id: '/api/chat'
      path: '/api/chat'
      fullPath: '/api/chat'
      preLoaderRoute: typeof ApiChatServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/users/$id': {
      id: '/api/users/$id'
      path: '/$id'
      fullPath: '/api/users/$id'
      preLoaderRoute: typeof ApiUsersIdServerRouteImport
      parentRoute: typeof ApiUsersServerRoute
    }
  }
}

interface PostsRouteRouteChildren {
  PostsPostIdRoute: typeof PostsPostIdRoute
  PostsIndexRoute: typeof PostsIndexRoute
}

const PostsRouteRouteChildren: PostsRouteRouteChildren = {
  PostsPostIdRoute: PostsPostIdRoute,
  PostsIndexRoute: PostsIndexRoute,
}

const PostsRouteRouteWithChildren = PostsRouteRoute._addFileChildren(
  PostsRouteRouteChildren,
)

interface UsersRouteRouteChildren {
  UsersUserIdRoute: typeof UsersUserIdRoute
  UsersIndexRoute: typeof UsersIndexRoute
}

const UsersRouteRouteChildren: UsersRouteRouteChildren = {
  UsersUserIdRoute: UsersUserIdRoute,
  UsersIndexRoute: UsersIndexRoute,
}

const UsersRouteRouteWithChildren = UsersRouteRoute._addFileChildren(
  UsersRouteRouteChildren,
)

interface PathlessLayoutNestedLayoutRouteChildren {
  PathlessLayoutNestedLayoutRouteARoute: typeof PathlessLayoutNestedLayoutRouteARoute
  PathlessLayoutNestedLayoutRouteBRoute: typeof PathlessLayoutNestedLayoutRouteBRoute
}

const PathlessLayoutNestedLayoutRouteChildren: PathlessLayoutNestedLayoutRouteChildren =
  {
    PathlessLayoutNestedLayoutRouteARoute:
      PathlessLayoutNestedLayoutRouteARoute,
    PathlessLayoutNestedLayoutRouteBRoute:
      PathlessLayoutNestedLayoutRouteBRoute,
  }

const PathlessLayoutNestedLayoutRouteWithChildren =
  PathlessLayoutNestedLayoutRoute._addFileChildren(
    PathlessLayoutNestedLayoutRouteChildren,
  )

interface PathlessLayoutRouteChildren {
  PathlessLayoutNestedLayoutRoute: typeof PathlessLayoutNestedLayoutRouteWithChildren
}

const PathlessLayoutRouteChildren: PathlessLayoutRouteChildren = {
  PathlessLayoutNestedLayoutRoute: PathlessLayoutNestedLayoutRouteWithChildren,
}

const PathlessLayoutRouteWithChildren = PathlessLayoutRoute._addFileChildren(
  PathlessLayoutRouteChildren,
)

interface ApiUsersServerRouteChildren {
  ApiUsersIdServerRoute: typeof ApiUsersIdServerRoute
}

const ApiUsersServerRouteChildren: ApiUsersServerRouteChildren = {
  ApiUsersIdServerRoute: ApiUsersIdServerRoute,
}

const ApiUsersServerRouteWithChildren = ApiUsersServerRoute._addFileChildren(
  ApiUsersServerRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  PostsRouteRoute: PostsRouteRouteWithChildren,
  UsersRouteRoute: UsersRouteRouteWithChildren,
  PathlessLayoutRoute: PathlessLayoutRouteWithChildren,
  ChatRoute: ChatRoute,
  DeferredRoute: DeferredRoute,
  RedirectRoute: RedirectRoute,
  PostsPostIdDeepRoute: PostsPostIdDeepRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
const rootServerRouteChildren: RootServerRouteChildren = {
  ApiChatServerRoute: ApiChatServerRoute,
  ApiChatTestServerRoute: ApiChatTestServerRoute,
  ApiUsersServerRoute: ApiUsersServerRouteWithChildren,
}
export const serverRouteTree = rootServerRouteImport
  ._addFileChildren(rootServerRouteChildren)
  ._addFileTypes<FileServerRouteTypes>()

export async function generateImageAndGetUrl(prompt: string): Promise<string> {
  console.log(`Generating image for prompt: "${prompt}"`);

  // Simulate API delay. Replace with your actual API call.
  await new Promise((resolve) => setTimeout(resolve, 3000));

  // Return a placeholder image URL for testing
  const seed = encodeURIComponent(prompt.slice(0, 15));
  return `https://picsum.photos/seed/${seed}/512/512`;
}

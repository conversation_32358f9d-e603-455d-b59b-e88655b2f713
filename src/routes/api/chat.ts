import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { createServerFileRoute } from '@tanstack/react-start/server';
import { streamText } from 'ai';

import { storyElementsSchema } from '~/lib/schemas';

const google = createGoogleGenerativeAI({
  apiKey: 'AIzaSyA049rXbCUFoGr0Hw2AfCxSgZHaQtuEU-U'
});

const narrationSeparator = '---NARRATION---';
const characterSeparator = '---CHARACTER_RESPONSE---';

const systemPrompt = `
You are an immersive, interactive AI storyteller.
Your goal is to create a compelling narrative that unfolds based on user choices.

Here is your process for every turn:
1. First, write the "Narration" describing the environment, events, and atmosphere. Narration is optional, only use it when necessary. Start it with '${narrationSeparator}' separator.
2. Immediately after the narration, on a new line, write '${characterSeparator}' followed by the spoken dialogue of any characters present. Character response is required.
3. After providing all the text, you MUST call the "showStoryElements" tool.
4. In the tool call, you will provide:
   - "imagePrompt": A detailed image prompt that visually captures the scene you just described
   - "userSuggestions": An array of three concise, engaging choices for the user to make next

IMPORTANT:
- Make sure to use the exact separators '${narrationSeparator}' and '${characterSeparator}'.
- Make sure to use the exact property names "imagePrompt" and "userSuggestions" in your tool call.
`;

function createStreamEncoder() {
  const encoder = new TextEncoder();
  return (eventName: string, data: string) => {
    const payload = JSON.stringify(data); // Always stringify data for consistency
    return encoder.encode(`event: ${eventName}\ndata: ${payload}\n\n`);
  };
}


export const ServerRoute = createServerFileRoute('/api/chat').methods({
  POST: async ({ request }) => {
    const { message } = (await request.json()) as {
      message: string;
    };

    const streamEncoder = createStreamEncoder();

    const customStream = new ReadableStream({
      async start(controller) {
        try {
          const result = streamText({
            model: google('gemini-2.5-flash'),
            system: systemPrompt,
            prompt: message,
            tools: {
              showStoryElements: {
                description: 'Displays the final story elements to the user.',
                parameters: storyElementsSchema,
              },
            },
          });

          // This state machine will track what part of the text we are currently streaming
          let parsingState: 'character' | 'narration' = 'narration';

          // Buffer to accumulate text across chunks to handle split separators
          let textBuffer = '';

          // Function to process accumulated text and handle separators
          const processBuffer = (buffer: string, isComplete = false) => {
            let remainingBuffer = buffer;

            while (remainingBuffer.length > 0) {
              if (parsingState === 'narration') {
                const charMarkerIndex = remainingBuffer.indexOf(characterSeparator);

                if (charMarkerIndex === -1) {
                  // No character separator found
                  if (isComplete) {
                    // This is the final chunk, send all remaining text as narration
                    if (remainingBuffer) {
                      controller.enqueue(streamEncoder('narration', remainingBuffer));
                    }
                    remainingBuffer = '';
                  } else {
                    // Keep potential partial separator in buffer
                    const maxSeparatorLength = Math.max(narrationSeparator.length, characterSeparator.length);
                    const safeLength = Math.max(0, remainingBuffer.length - maxSeparatorLength + 1);

                    if (safeLength > 0) {
                      const safeText = remainingBuffer.slice(0, safeLength);
                      controller.enqueue(streamEncoder('narration', safeText));
                      remainingBuffer = remainingBuffer.slice(safeLength);
                    }
                    break; // Keep remaining in buffer for next chunk
                  }
                } else {
                  // Character separator found
                  const narrationPart = remainingBuffer.slice(0, charMarkerIndex);

                  // Send narration part if any
                  if (narrationPart) {
                    controller.enqueue(streamEncoder('narration', narrationPart));
                  }

                  // Switch to character state and continue processing
                  parsingState = 'character';
                  remainingBuffer = remainingBuffer.slice(charMarkerIndex + characterSeparator.length);
                }
              } else {
                // In character state - check for narration separator to switch back
                const narMarkerIndex = remainingBuffer.indexOf(narrationSeparator);

                if (narMarkerIndex === -1) {
                  // No narration separator found
                  if (isComplete) {
                    // Send all remaining text as character
                    if (remainingBuffer) {
                      controller.enqueue(streamEncoder('character', remainingBuffer));
                    }
                    remainingBuffer = '';
                  } else {
                    // Keep potential partial separator in buffer
                    const maxSeparatorLength = Math.max(narrationSeparator.length, characterSeparator.length);
                    const safeLength = Math.max(0, remainingBuffer.length - maxSeparatorLength + 1);

                    if (safeLength > 0) {
                      const safeText = remainingBuffer.slice(0, safeLength);
                      controller.enqueue(streamEncoder('character', safeText));
                      remainingBuffer = remainingBuffer.slice(safeLength);
                    }
                    break; // Keep remaining in buffer for next chunk
                  }
                } else {
                  // Narration separator found
                  const characterPart = remainingBuffer.slice(0, narMarkerIndex);

                  // Send character part if any
                  if (characterPart) {
                    controller.enqueue(streamEncoder('character', characterPart));
                  }

                  // Switch to narration state and continue processing
                  parsingState = 'narration';
                  remainingBuffer = remainingBuffer.slice(narMarkerIndex + narrationSeparator.length);
                }
              }
            }

            return remainingBuffer;
          };

          // Iterate through the raw stream from the Vercel AI SDK
          for await (const part of result.fullStream) {
            console.log({ part })
            switch (part.type) {
              case 'text': {
                const text = part.text as string;

                // Add new text to buffer
                textBuffer += text;

                // Process the buffer, keeping any remaining text for next iteration
                textBuffer = processBuffer(textBuffer, false);

                break;
              }

              case 'tool-call': {
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
                if (part.toolName === 'showStoryElements') {
                  // The structured data is ready. Send it as a single 'data' event.
                  // We don't need to validate with Zod here if we trust the SDK,
                  // but it's good practice if you want to be extra safe.
                  controller.enqueue(streamEncoder('data', JSON.stringify(part.input)));
                }
                break;
              }

              case 'error': {
                console.error('Stream error:', part.error);
                controller.enqueue(streamEncoder('error', JSON.stringify({ message: 'An error occurred during the stream.' })));
                break;
              }
            }
          }
        } catch (error) {
          console.error('Error in chat API:', error);
          controller.enqueue(streamEncoder('error', JSON.stringify({ message: 'An internal server error occurred.' })));
        } finally {
          // Signal the end of the stream to the client and close the stream
          controller.enqueue(streamEncoder('end', 'Stream finished.'));
          controller.close();
        }
      },
    });

    return new Response(customStream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });

  },
});

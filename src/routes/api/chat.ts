import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { createServerFileRoute } from '@tanstack/react-start/server';
import { streamText } from 'ai';

import { storyElementsSchema } from '~/lib/schemas';

const google = createGoogleGenerativeAI({
  apiKey: 'AIzaSyA049rXbCUFoGr0Hw2AfCxSgZHaQtuEU-U'
});

const narrationSeparator = '---NARRATION---';
const characterSeparator = '---CHARACTER_RESPONSE---';

const systemPrompt = `
You are an immersive, interactive AI storyteller.
Your goal is to create a compelling narrative that unfolds based on user choices.

Here is your process for every turn:
1. First, write the "Narration" describing the environment, events, and atmosphere. Narration is optional, only use it when necessary. Start it with '${narrationSeparator}' separator.
2. Immediately after the narration, on a new line, write '${characterSeparator}' followed by the spoken dialogue of any characters present. Character response is required.
3. After providing all the text, you MUST call the "showStoryElements" tool.
4. In the tool call, you will provide:
   - "imagePrompt": A detailed image prompt that visually captures the scene you just described
   - "userSuggestions": An array of three concise, engaging choices for the user to make next

IMPORTANT:
- Make sure to use the exact separators '${narrationSeparator}' and '${characterSeparator}'.
- Make sure to use the exact property names "imagePrompt" and "userSuggestions" in your tool call.
`;

function createStreamEncoder() {
  const encoder = new TextEncoder();
  return (eventName: string, data: string) => {
    const payload = JSON.stringify(data); // Always stringify data for consistency
    return encoder.encode(`event: ${eventName}\ndata: ${payload}\n\n`);
  };
}

export const ServerRoute = createServerFileRoute('/api/chat').methods({
  POST: async ({ request }) => {
    const { message } = (await request.json()) as {
      message: string;
    };

    const streamEncoder = createStreamEncoder();

    const customStream = new ReadableStream({
      async start(controller) {
        try {
          const result = streamText({
            model: google('gemini-2.5-flash'),
            system: systemPrompt,
            prompt: message,
            tools: {
              showStoryElements: {
                description: 'Displays the final story elements to the user.',
                parameters: storyElementsSchema,
              },
            },
          });

          // Buffer to accumulate text across chunks to handle split separators
          let textBuffer = '';
          let currentMode: 'character' | 'narration' = 'narration';
          let hasStartedNarration = false;

          // Function to process accumulated text and handle separators
          const processBuffer = (buffer: string, isComplete = false) => {
            let remainingBuffer = buffer;

            while (remainingBuffer.length > 0) {
              // Look for both separators in the current buffer
              const narrationIndex = remainingBuffer.indexOf(narrationSeparator);
              const characterIndex = remainingBuffer.indexOf(characterSeparator);

              // Find the earliest separator
              let nextSeparatorIndex = -1;
              let nextSeparatorType: 'character' | 'narration' | null = null;
              let nextSeparatorLength = 0;

              if (narrationIndex !== -1 && (characterIndex === -1 || narrationIndex < characterIndex)) {
                nextSeparatorIndex = narrationIndex;
                nextSeparatorType = 'narration';
                nextSeparatorLength = narrationSeparator.length;
              } else if (characterIndex !== -1) {
                nextSeparatorIndex = characterIndex;
                nextSeparatorType = 'character';
                nextSeparatorLength = characterSeparator.length;
              }

              if (nextSeparatorIndex === -1) {
                // No separator found
                if (isComplete) {
                  // Send all remaining text in current mode
                  if (remainingBuffer.trim()) {
                    controller.enqueue(streamEncoder(currentMode, remainingBuffer));
                  }
                  remainingBuffer = '';
                } else {
                  // Keep potential partial separator in buffer
                  const maxSeparatorLength = Math.max(narrationSeparator.length, characterSeparator.length);
                  const safeLength = Math.max(0, remainingBuffer.length - maxSeparatorLength + 1);

                  if (safeLength > 0) {
                    const safeText = remainingBuffer.slice(0, safeLength);
                    if (safeText.trim()) {
                      controller.enqueue(streamEncoder(currentMode, safeText));
                    }
                    remainingBuffer = remainingBuffer.slice(safeLength);
                  }
                  break; // Keep remaining in buffer for next chunk
                }
              } else {
                // Separator found
                const textBeforeSeparator = remainingBuffer.slice(0, nextSeparatorIndex);

                // Send text before separator if any (and we're not at the very beginning with narration separator)
                if (textBeforeSeparator.trim() && !(nextSeparatorType === 'narration' && !hasStartedNarration)) {
                  controller.enqueue(streamEncoder(currentMode, textBeforeSeparator));
                }

                // Update mode based on separator found
                if (nextSeparatorType === 'narration') {
                  currentMode = 'narration';
                  hasStartedNarration = true;
                } else {
                  currentMode = 'character';
                }

                // Continue processing after the separator
                remainingBuffer = remainingBuffer.slice(nextSeparatorIndex + nextSeparatorLength);
              }
            }

            return remainingBuffer;
          };

          // Iterate through the raw stream from the Vercel AI SDK - THIS IS REAL-TIME STREAMING
          for await (const part of result.fullStream) {
            console.log({ part });
            switch (part.type) {
              case 'text': {
                const text = part.text as string;

                // Add new text to buffer
                textBuffer += text;

                // Process the buffer IMMEDIATELY, keeping any remaining text for next iteration
                // This ensures we stream content to frontend as soon as it's available
                textBuffer = processBuffer(textBuffer, false);

                break;
              }

              case 'tool-call': {
                if (part.toolName === 'showStoryElements') {
                  // The structured data is ready. Send it as a single 'data' event.
                  controller.enqueue(streamEncoder('data', JSON.stringify(part.input)));
                }
                break;
              }

              case 'error': {
                console.error('Stream error:', part.error);
                controller.enqueue(streamEncoder('error', JSON.stringify({ message: 'An error occurred during the stream.' })));
                break;
              }
            }
          }

          // Process any remaining text in the buffer when stream ends
          if (textBuffer.length > 0) {
            processBuffer(textBuffer, true);
          }

        } catch (error) {
          console.error('Error in chat API:', error);
          controller.enqueue(streamEncoder('error', JSON.stringify({ message: 'An internal server error occurred.' })));
        } finally {
          // Signal the end of the stream to the client and close the stream
          controller.enqueue(streamEncoder('end', 'Stream finished.'));
          controller.close();
        }
      },
    });

    return new Response(customStream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
  },
});

import { createFileRoute, <PERSON>, Outlet } from '@tanstack/react-router';

export const Route = createFileRoute('/_pathlessLayout/_nested-layout')({
  component: PathlessLayoutComponent,
});

function PathlessLayoutComponent() {
  return (
    <div>
      <div>I'm a nested pathless layout</div>
      <div className="flex gap-2">
        <Link
          activeProps={{
            className: 'font-bold',
          }}
          to="/route-a"
        >
          Go to route A
        </Link>
        <Link
          activeProps={{
            className: 'font-bold',
          }}
          to="/route-b"
        >
          Go to route B
        </Link>
      </div>
      <div>
        <Outlet />
      </div>
    </div>
  );
}

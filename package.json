{"name": "tanstack-start-example-basic-react-query", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "vite build && tsc --noEmit", "check-format": "pnpm format --list-different", "dev": "vite dev", "format": "prettier --cache --write package.json eslint.config.js \"{src,apps,libs,test}/**/*.{ts,tsx,json}\"", "lint": "eslint --cache --report-unused-disable-directives -c eslint.config.js \"src/**/*.{ts,tsx,graphql}\"", "lint:fix": "eslint --cache --report-unused-disable-directives --fix -c eslint.config.js \"src/**/*.{ts,tsx}\"", "start": "vite start"}, "dependencies": {"@ai-sdk/google": "2.0.0-beta.4", "@ai-sdk/openai": "2.0.0-beta.3", "@ai-sdk/react": "2.0.0-beta.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-router": "^1.123.0", "@tanstack/react-router-with-query": "^1.123.0", "@tanstack/react-start": "^1.123.0", "ai": "5.0.0-beta.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "motion": "^12.19.3", "react": "^19.1.0", "react-dom": "^19.1.0", "redaxios": "^0.5.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint-react/eslint-plugin": "^1.52.2", "@eslint/js": "^9.30.0", "@sentry/vite-plugin": "^3.5.0", "@spotlightjs/sidecar": "^1.11.4", "@spotlightjs/spotlight": "^3.0.1", "@stylistic/eslint-plugin": "^5.1.0", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@tanstack/eslint-plugin-query": "^5.81.2", "@tanstack/eslint-plugin-router": "^1.121.21", "@tanstack/react-router-devtools": "^1.123.0", "@total-typescript/ts-reset": "^0.6.1", "@types/node": "^24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/parser": "^8.35.0", "babel-plugin-react-compiler": "19.1.0-rc.2", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-node": "^0.3.9", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-depend": "^1.2.0", "eslint-plugin-es-x": "^8.7.0", "eslint-plugin-import-x": "^4.16.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-n": "^17.20.0", "eslint-plugin-perfectionist": "^4.15.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react-compiler": "19.1.0-rc.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unicorn": "^59.0.1", "globals": "^16.2.0", "postcss": "^8.5.6", "prettier-plugin-packagejson": "^2.5.17", "prettier-plugin-tailwindcss": "^0.6.13", "react-scan": "^0.4.3", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "vite": "^7.0.0", "vite-bundle-analyzer": "^0.23.0", "vite-plugin-mkcert": "^1.17.8", "vite-tsconfig-paths": "^5.1.4"}}